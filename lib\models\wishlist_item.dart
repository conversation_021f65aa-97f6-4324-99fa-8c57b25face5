import 'package:cloud_firestore/cloud_firestore.dart';
import 'product.dart';

class WishlistItem {
  final String id;
  final String productId;
  final Product product;
  final DateTime addedAt;
  final String? notes; // User notes about the item
  final Map<String, dynamic>? metadata;

  const WishlistItem({
    required this.id,
    required this.productId,
    required this.product,
    required this.addedAt,
    this.notes,
    this.metadata,
  });

  /// Check if the product is still available
  bool get isAvailable => product.isActive && product.stock > 0;

  /// Check if the product price has changed (for notifications)
  bool hasPriceChanged(double originalPrice) {
    return product.price != originalPrice;
  }

  /// Create WishlistItem from Firestore document
  factory WishlistItem.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return WishlistItem(
      id: doc.id,
      productId: data['productId'] ?? '',
      product: Product.fromMap(data['product'] ?? {}),
      addedAt: (data['addedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      notes: data['notes'],
      metadata: data['metadata'],
    );
  }

  /// Create WishlistItem from Map (for local storage)
  factory WishlistItem.fromMap(Map<String, dynamic> map) {
    return WishlistItem(
      id: map['id'] ?? '',
      productId: map['productId'] ?? '',
      product: Product.fromMap(map['product'] ?? {}),
      addedAt: DateTime.parse(map['addedAt'] ?? DateTime.now().toIso8601String()),
      notes: map['notes'],
      metadata: map['metadata'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'productId': productId,
      'product': product.toFirestore(),
      'addedAt': Timestamp.fromDate(addedAt),
      'notes': notes,
      'metadata': metadata,
    };
  }

  /// Convert to Map (for local storage)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'product': product.toFirestore(),
      'addedAt': addedAt.toIso8601String(),
      'notes': notes,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated values
  WishlistItem copyWith({
    String? id,
    String? productId,
    Product? product,
    DateTime? addedAt,
    String? notes,
    Map<String, dynamic>? metadata,
  }) {
    return WishlistItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      addedAt: addedAt ?? this.addedAt,
      notes: notes ?? this.notes,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WishlistItem &&
        other.id == id &&
        other.productId == productId;
  }

  @override
  int get hashCode => id.hashCode ^ productId.hashCode;

  @override
  String toString() {
    return 'WishlistItem(id: $id, productId: $productId, addedAt: $addedAt)';
  }
}
