# Cloudinary Setup Guide for dekuMArt

This guide will help you set up Cloudinary for image uploads in the dekuMArt app with upload progress indicators.

## 🚀 Features Implemented

- ✅ **Real-time upload progress** - Visual progress indicators for each image
- ✅ **Multiple image upload** - Support for up to 5 product images
- ✅ **Image optimization** - Automatic compression and format optimization
- ✅ **Error handling** - Comprehensive error handling with user feedback
- ✅ **Upload states** - Different UI states for uploading, success, and errors
- ✅ **Cloudinary integration** - Professional cloud image management

## 📋 Prerequisites

1. Flutter development environment set up
2. A Cloudinary account (free tier available)

## 🔧 Setup Instructions

### Step 1: Create Cloudinary Account

1. Go to [Cloudinary.com](https://cloudinary.com/)
2. Sign up for a free account
3. Verify your email address

### Step 2: Get Your Credentials

1. Log in to your Cloudinary dashboard
2. Copy the following from your dashboard:
   - **Cloud Name**
   - **API Key** 
   - **API Secret**

### Step 3: Create Upload Preset

1. In your Cloudinary dashboard, go to **Settings** → **Upload**
2. Click **"Add upload preset"**
3. Configure the preset:
   - **Preset name**: `dekumart_products`
   - **Signing mode**: `Unsigned`
   - **Folder**: `dekumart/products`
   - **Allowed formats**: `jpg, png, jpeg, webp`
   - **Max file size**: `10MB`
   - **Auto optimization**: `Enabled`
4. Save the preset

### Step 4: Configure the App

1. Open `lib/config/cloudinary_config.dart`
2. Replace the placeholder values:

```dart
class CloudinaryConfig {
  static const String cloudName = 'your-actual-cloud-name';
  static const String apiKey = 'your-actual-api-key';
  static const String apiSecret = 'your-actual-api-secret';
  static const String uploadPreset = 'dekumart_products';
  // ... rest of the config
}
```

### Step 5: Test the Implementation

1. Run the app: `flutter run`
2. Navigate to the post product screen
3. Add some images and fill out the form
4. Submit and watch the upload progress!

## 🎯 How It Works

### Upload Process

1. **Image Selection**: Users can select up to 5 images from camera or gallery
2. **Form Validation**: All required fields are validated before upload
3. **Progress Tracking**: Each image shows individual upload progress
4. **Cloudinary Upload**: Images are uploaded to Cloudinary with optimization
5. **Success Feedback**: Visual indicators show successful uploads
6. **Product Creation**: Product data is saved with Cloudinary URLs

### Progress Indicators

- **Circular Progress**: Shows percentage completion for each image
- **Upload States**: Different UI states for uploading, success, and idle
- **Button States**: Submit button shows different text based on current operation
- **Error Handling**: Clear error messages for failed uploads

## 📱 UI Features

### Image Upload Section
- Horizontal scrollable image picker
- "MAIN" label on first image
- Upload progress overlay with percentage
- Success checkmark when complete
- Remove button when not uploading

### Submit Button States
- **"Post Product"** - Ready to submit
- **"Uploading Images..."** - Images being uploaded to Cloudinary
- **"Posting Product..."** - Saving product data to database

### Progress Tracking
```dart
// Individual image progress
final Map<int, double> _uploadProgress = {};

// Upload progress callback
onProgress: (progress) {
  setState(() {
    _uploadProgress[i] = progress;
  });
}
```

## 🔒 Security Best Practices

### For Development
- Use unsigned upload presets for simplicity
- Restrict upload presets to specific folders
- Set reasonable file size limits

### For Production
- Consider using signed uploads for sensitive data
- Implement server-side validation
- Use environment variables for credentials
- Add rate limiting for uploads

## 🛠 Customization Options

### Image Transformations
```dart
// Modify in CloudinaryConfig
static const String thumbnailTransform = 'w_300,h_300,c_fill,q_auto:good,f_auto';
static const String mediumTransform = 'w_600,h_600,c_fit,q_auto:good,f_auto';
```

### Upload Settings
```dart
// Modify in CloudinaryConfig
static const int maxImageSize = 1024; // Max width/height
static const int imageQuality = 85; // Quality (0-100)
```

### Progress UI
```dart
// Customize progress indicator in _buildImageCard
CircularProgressIndicator(
  value: progress,
  strokeWidth: 3,
  backgroundColor: Colors.white.withValues(alpha: 0.3),
  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
)
```

## 🐛 Troubleshooting

### Common Issues

1. **Upload fails with 401 error**
   - Check your API credentials
   - Ensure upload preset exists and is unsigned

2. **Images not showing progress**
   - Verify internet connection
   - Check Cloudinary dashboard for upload logs

3. **App crashes on image selection**
   - Add camera/gallery permissions to platform files
   - Test on physical device for camera functionality

### Debug Tips

1. Enable Dio logging to see upload requests
2. Check Cloudinary dashboard for upload activity
3. Use Flutter inspector to debug UI states

## 📚 Additional Resources

- [Cloudinary Flutter Documentation](https://cloudinary.com/documentation/flutter_integration)
- [Dio Package Documentation](https://pub.dev/packages/dio)
- [Image Picker Documentation](https://pub.dev/packages/image_picker)

## 🎉 Next Steps

1. Integrate with Firebase Firestore for product storage
2. Add image editing capabilities
3. Implement image caching for better performance
4. Add batch upload optimization
5. Create image gallery for product viewing
