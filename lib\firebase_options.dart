// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCrqtKWfla7KwqDZFSdBehcwoJw05MwPXg',
    appId: '1:125074351156:web:99a5e9c6574a1f71461661',
    messagingSenderId: '125074351156',
    projectId: 'dekumart-4ba5b',
    authDomain: 'dekumart-4ba5b.firebaseapp.com',
    storageBucket: 'dekumart-4ba5b.firebasestorage.app',
    measurementId: 'G-LEJJHECV3J',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDJVKEg3mOFOr1EfWp-HdZlF-F_wQcaKo0',
    appId: '1:125074351156:android:7a4d9cb3c624b850461661',
    messagingSenderId: '125074351156',
    projectId: 'dekumart-4ba5b',
    storageBucket: 'dekumart-4ba5b.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAPOSllOnw0r0LTagZvq4Rqv6Elkhc1HPI',
    appId: '1:125074351156:ios:5fa078b19f51de13461661',
    messagingSenderId: '125074351156',
    projectId: 'dekumart-4ba5b',
    storageBucket: 'dekumart-4ba5b.firebasestorage.app',
    iosClientId: '125074351156-ftrq64kjno1m44cpedvr3knhq7h14pev.apps.googleusercontent.com',
    iosBundleId: 'com.example.dekumartApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAPOSllOnw0r0LTagZvq4Rqv6Elkhc1HPI',
    appId: '1:125074351156:ios:5fa078b19f51de13461661',
    messagingSenderId: '125074351156',
    projectId: 'dekumart-4ba5b',
    storageBucket: 'dekumart-4ba5b.firebasestorage.app',
    iosClientId: '125074351156-ftrq64kjno1m44cpedvr3knhq7h14pev.apps.googleusercontent.com',
    iosBundleId: 'com.example.dekumartApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCrqtKWfla7KwqDZFSdBehcwoJw05MwPXg',
    appId: '1:125074351156:web:a97fbe7380a0b09d461661',
    messagingSenderId: '125074351156',
    projectId: 'dekumart-4ba5b',
    authDomain: 'dekumart-4ba5b.firebaseapp.com',
    storageBucket: 'dekumart-4ba5b.firebasestorage.app',
    measurementId: 'G-0007Y979FJ',
  );

}