import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/cart_item.dart';
import '../models/product.dart';
import '../services/cart_service.dart';

class CartProvider extends ChangeNotifier {
  final CartService _cartService = CartService();
  List<CartItem> _items = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<CartItem> get items => _items;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get itemCount => _items.length;
  int get totalQuantity => _items.fold(0, (sum, item) => sum + item.quantity);
  
  double get totalPrice => _items.fold(0.0, (sum, item) => sum + item.totalPrice);
  double get subtotal => totalPrice;
  double get tax => totalPrice * 0.16; // 16% VAT in Kenya
  double get shipping => totalPrice > 1000 ? 0 : 200; // Free shipping over KSh 1000
  double get grandTotal => subtotal + tax + shipping;

  // Check if product is in cart
  bool isInCart(String productId) {
    return _items.any((item) => item.productId == productId);
  }

  // Get cart item by product ID
  CartItem? getCartItem(String productId) {
    try {
      return _items.firstWhere((item) => item.productId == productId);
    } catch (e) {
      return null;
    }
  }

  // Get quantity of specific product in cart
  int getProductQuantity(String productId) {
    final item = getCartItem(productId);
    return item?.quantity ?? 0;
  }

  // Initialize cart (load from local storage and sync with server)
  Future<void> initializeCart() async {
    _setLoading(true);
    try {
      // Load from local storage first for immediate UI update
      await _loadFromLocalStorage();
      
      // Then sync with server
      await _syncWithServer();
    } catch (e) {
      _setError('Failed to initialize cart: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add item to cart
  Future<void> addToCart(Product product, {int quantity = 1}) async {
    try {
      final existingItem = getCartItem(product.id);
      
      if (existingItem != null) {
        // Update quantity if item already exists
        await updateQuantity(product.id, existingItem.quantity + quantity);
      } else {
        // Add new item
        final cartItem = CartItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          productId: product.id,
          product: product,
          quantity: quantity,
          unitPrice: product.price,
          addedAt: DateTime.now(),
        );
        
        _items.add(cartItem);
        await _saveToLocalStorage();
        await _cartService.addToCart(cartItem);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to add item to cart: $e');
    }
  }

  // Remove item from cart
  Future<void> removeFromCart(String productId) async {
    try {
      final itemIndex = _items.indexWhere((item) => item.productId == productId);
      if (itemIndex != -1) {
        final item = _items[itemIndex];
        _items.removeAt(itemIndex);
        await _saveToLocalStorage();
        await _cartService.removeFromCart(item.id);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to remove item from cart: $e');
    }
  }

  // Update item quantity
  Future<void> updateQuantity(String productId, int newQuantity) async {
    try {
      if (newQuantity <= 0) {
        await removeFromCart(productId);
        return;
      }

      final itemIndex = _items.indexWhere((item) => item.productId == productId);
      if (itemIndex != -1) {
        final oldItem = _items[itemIndex];
        final updatedItem = oldItem.copyWith(quantity: newQuantity);
        _items[itemIndex] = updatedItem;
        await _saveToLocalStorage();
        await _cartService.updateCartItem(updatedItem);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update quantity: $e');
    }
  }

  // Clear entire cart
  Future<void> clearCart() async {
    try {
      _items.clear();
      await _saveToLocalStorage();
      await _cartService.clearCart();
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear cart: $e');
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Load cart from local storage
  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartData = prefs.getString('cart_items');
      if (cartData != null) {
        final List<dynamic> itemsJson = json.decode(cartData);
        _items = itemsJson.map((json) => CartItem.fromMap(json)).toList();
        notifyListeners();
      }
    } catch (e) {
      print('Error loading cart from local storage: $e');
    }
  }

  // Save cart to local storage
  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final itemsJson = _items.map((item) => item.toMap()).toList();
      await prefs.setString('cart_items', json.encode(itemsJson));
    } catch (e) {
      print('Error saving cart to local storage: $e');
    }
  }

  // Sync with server
  Future<void> _syncWithServer() async {
    try {
      final serverItems = await _cartService.getCartItems();
      _items = serverItems;
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      print('Error syncing cart with server: $e');
      // Continue with local data if server sync fails
    }
  }

  // Refresh cart from server
  Future<void> refreshCart() async {
    _setLoading(true);
    try {
      await _syncWithServer();
    } catch (e) {
      _setError('Failed to refresh cart: $e');
    } finally {
      _setLoading(false);
    }
  }
}
