import 'package:cloud_firestore/cloud_firestore.dart';
import 'product.dart';

class CartItem {
  final String id;
  final String productId;
  final Product product;
  final int quantity;
  final double unitPrice;
  final DateTime addedAt;
  final String? selectedVariant; // For future use (size, color, etc.)
  final Map<String, dynamic>? metadata;

  const CartItem({
    required this.id,
    required this.productId,
    required this.product,
    required this.quantity,
    required this.unitPrice,
    required this.addedAt,
    this.selectedVariant,
    this.metadata,
  });

  /// Calculate total price for this cart item
  double get totalPrice => unitPrice * quantity;

  /// Check if item is available (in stock)
  bool get isAvailable => product.stock >= quantity;

  /// Create CartItem from Firestore document
  factory CartItem.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CartItem(
      id: doc.id,
      productId: data['productId'] ?? '',
      product: Product.fromMap(data['product'] ?? {}),
      quantity: data['quantity'] ?? 1,
      unitPrice: (data['unitPrice'] ?? 0.0).toDouble(),
      addedAt: (data['addedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      selectedVariant: data['selectedVariant'],
      metadata: data['metadata'],
    );
  }

  /// Create CartItem from Map (for local storage)
  factory CartItem.fromMap(Map<String, dynamic> map) {
    return CartItem(
      id: map['id'] ?? '',
      productId: map['productId'] ?? '',
      product: Product.fromMap(map['product'] ?? {}),
      quantity: map['quantity'] ?? 1,
      unitPrice: (map['unitPrice'] ?? 0.0).toDouble(),
      addedAt: DateTime.parse(map['addedAt'] ?? DateTime.now().toIso8601String()),
      selectedVariant: map['selectedVariant'],
      metadata: map['metadata'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'productId': productId,
      'product': product.toFirestore(),
      'quantity': quantity,
      'unitPrice': unitPrice,
      'addedAt': Timestamp.fromDate(addedAt),
      'selectedVariant': selectedVariant,
      'metadata': metadata,
    };
  }

  /// Convert to Map (for local storage)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'product': product.toFirestore(),
      'quantity': quantity,
      'unitPrice': unitPrice,
      'addedAt': addedAt.toIso8601String(),
      'selectedVariant': selectedVariant,
      'metadata': metadata,
    };
  }

  /// Create a copy with updated values
  CartItem copyWith({
    String? id,
    String? productId,
    Product? product,
    int? quantity,
    double? unitPrice,
    DateTime? addedAt,
    String? selectedVariant,
    Map<String, dynamic>? metadata,
  }) {
    return CartItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      addedAt: addedAt ?? this.addedAt,
      selectedVariant: selectedVariant ?? this.selectedVariant,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem &&
        other.id == id &&
        other.productId == productId &&
        other.quantity == quantity;
  }

  @override
  int get hashCode => id.hashCode ^ productId.hashCode ^ quantity.hashCode;

  @override
  String toString() {
    return 'CartItem(id: $id, productId: $productId, quantity: $quantity, totalPrice: $totalPrice)';
  }
}
