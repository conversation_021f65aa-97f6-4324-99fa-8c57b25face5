import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/seller.dart';
import '../services/firebase_service.dart';

/// Service class for handling seller profile operations
class SellerService {
  static final SellerService _instance = SellerService._internal();
  factory SellerService() => _instance;
  SellerService._internal();

  final FirebaseService _firebaseService = FirebaseService();

  /// Get sellers collection reference
  CollectionReference get sellersCollection => 
      _firebaseService.firestore.collection('sellers');

  /// Create or update seller profile
  Future<String> createOrUpdateSellerProfile(SellerProfile seller) async {
    try {
      await sellersCollection.doc(seller.userId).set(
        seller.toFirestore(),
        SetOptions(merge: true),
      );
      return seller.userId;
    } catch (e) {
      throw Exception('Failed to create/update seller profile: $e');
    }
  }

  /// Get seller profile by user ID
  Future<SellerProfile?> getSellerProfile(String userId) async {
    try {
      final doc = await sellersCollection.doc(userId).get();
      if (doc.exists) {
        return SellerProfile.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get seller profile: $e');
    }
  }

  /// Update seller profile
  Future<void> updateSellerProfile(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      updates['lastSeen'] = Timestamp.fromDate(DateTime.now());
      await sellersCollection.doc(userId).update(updates);
    } catch (e) {
      throw Exception('Failed to update seller profile: $e');
    }
  }

  /// Update seller online status
  Future<void> updateOnlineStatus(String userId, bool isOnline) async {
    try {
      await sellersCollection.doc(userId).update({
        'isOnline': isOnline,
        'lastSeen': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update online status: $e');
    }
  }

  /// Increment seller sales count
  Future<void> incrementSalesCount(String userId) async {
    try {
      await sellersCollection.doc(userId).update({
        'totalSales': FieldValue.increment(1),
        'lastSeen': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to increment sales count: $e');
    }
  }

  /// Update seller product count
  Future<void> updateProductCount(String userId, int count) async {
    try {
      await sellersCollection.doc(userId).update({
        'totalProducts': count,
        'lastSeen': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update product count: $e');
    }
  }

  /// Update seller rating
  Future<void> updateSellerRating(String userId, double newRating) async {
    try {
      await sellersCollection.doc(userId).update({
        'rating': newRating,
        'totalRatings': FieldValue.increment(1),
        'lastSeen': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update seller rating: $e');
    }
  }

  /// Get top sellers
  Future<List<SellerProfile>> getTopSellers({int limit = 10}) async {
    try {
      final querySnapshot = await sellersCollection
          .where('isActive', isEqualTo: true)
          .orderBy('rating', descending: true)
          .orderBy('totalSales', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => SellerProfile.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get top sellers: $e');
    }
  }

  /// Search sellers by name or specialties
  Future<List<SellerProfile>> searchSellers(
    String query, {
    int limit = 20,
  }) async {
    try {
      // Basic search - for better search, consider using Algolia
      final querySnapshot = await sellersCollection
          .where('isActive', isEqualTo: true)
          .orderBy('rating', descending: true)
          .limit(limit * 2)
          .get();

      final sellers = querySnapshot.docs
          .map((doc) => SellerProfile.fromFirestore(doc))
          .toList();

      // Filter locally for name or specialties match
      final filteredSellers = sellers.where((seller) {
        final searchLower = query.toLowerCase();
        return seller.name.toLowerCase().contains(searchLower) ||
               seller.specialties.any((specialty) => 
                   specialty.toLowerCase().contains(searchLower)) ||
               (seller.bio?.toLowerCase().contains(searchLower) ?? false);
      }).take(limit).toList();

      return filteredSellers;
    } catch (e) {
      throw Exception('Failed to search sellers: $e');
    }
  }

  /// Get sellers by location
  Future<List<SellerProfile>> getSellersByLocation(
    String location, {
    int limit = 20,
  }) async {
    try {
      final querySnapshot = await sellersCollection
          .where('location', isEqualTo: location)
          .where('isActive', isEqualTo: true)
          .orderBy('rating', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => SellerProfile.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get sellers by location: $e');
    }
  }

  /// Calculate and update response rate
  Future<void> updateResponseRate(String userId, bool responded) async {
    try {
      final doc = await sellersCollection.doc(userId).get();
      if (!doc.exists) return;

      final seller = SellerProfile.fromFirestore(doc);
      final currentRate = seller.responseRate;
      final totalInteractions = seller.metadata?['totalInteractions'] ?? 0;
      
      final newTotal = totalInteractions + 1;
      final newRate = responded 
          ? ((currentRate * totalInteractions) + 1) / newTotal
          : (currentRate * totalInteractions) / newTotal;

      await sellersCollection.doc(userId).update({
        'responseRate': newRate,
        'metadata.totalInteractions': newTotal,
        'lastSeen': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update response rate: $e');
    }
  }

  /// Update response time category
  Future<void> updateResponseTime(String userId, String responseTime) async {
    try {
      await sellersCollection.doc(userId).update({
        'responseTime': responseTime,
        'lastSeen': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update response time: $e');
    }
  }

  /// Stream seller profile for real-time updates
  Stream<SellerProfile?> streamSellerProfile(String userId) {
    return sellersCollection
        .doc(userId)
        .snapshots()
        .map((doc) => doc.exists ? SellerProfile.fromFirestore(doc) : null);
  }

  /// Get seller statistics
  Future<Map<String, dynamic>> getSellerStats(String userId) async {
    try {
      final seller = await getSellerProfile(userId);
      if (seller == null) {
        return {
          'totalProducts': 0,
          'totalSales': 0,
          'rating': 0.0,
          'responseRate': 0.0,
          'joinedDaysAgo': 0,
        };
      }

      final joinedDaysAgo = DateTime.now().difference(seller.joinedAt).inDays;

      return {
        'totalProducts': seller.totalProducts,
        'totalSales': seller.totalSales,
        'rating': seller.rating,
        'responseRate': seller.responseRate,
        'joinedDaysAgo': joinedDaysAgo,
        'isVerified': seller.isVerified,
        'specialties': seller.specialties,
      };
    } catch (e) {
      throw Exception('Failed to get seller statistics: $e');
    }
  }
}
