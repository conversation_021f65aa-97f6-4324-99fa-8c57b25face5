import 'package:cloud_firestore/cloud_firestore.dart';
import 'message.dart';

enum ChatType { direct, group }

class ChatParticipant {
  final String userId;
  final String name;
  final String? avatar;
  final DateTime joinedAt;
  final DateTime? lastSeen;
  final bool isOnline;
  final bool isTyping;
  final DateTime? lastReadAt;
  final bool isMuted;

  const ChatParticipant({
    required this.userId,
    required this.name,
    this.avatar,
    required this.joinedAt,
    this.lastSeen,
    this.isOnline = false,
    this.isTyping = false,
    this.lastReadAt,
    this.isMuted = false,
  });

  factory ChatParticipant.fromMap(Map<String, dynamic> data) {
    return ChatParticipant(
      userId: data['userId'] ?? '',
      name: data['name'] ?? '',
      avatar: data['avatar'],
      joinedAt: (data['joinedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastSeen: (data['lastSeen'] as Timestamp?)?.toDate(),
      isOnline: data['isOnline'] ?? false,
      isTyping: data['isTyping'] ?? false,
      lastReadAt: (data['lastReadAt'] as Timestamp?)?.toDate(),
      isMuted: data['isMuted'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'name': name,
      'avatar': avatar,
      'joinedAt': Timestamp.fromDate(joinedAt),
      'lastSeen': lastSeen != null ? Timestamp.fromDate(lastSeen!) : null,
      'isOnline': isOnline,
      'isTyping': isTyping,
      'lastReadAt': lastReadAt != null ? Timestamp.fromDate(lastReadAt!) : null,
      'isMuted': isMuted,
    };
  }

  ChatParticipant copyWith({
    String? userId,
    String? name,
    String? avatar,
    DateTime? joinedAt,
    DateTime? lastSeen,
    bool? isOnline,
    bool? isTyping,
    DateTime? lastReadAt,
    bool? isMuted,
  }) {
    return ChatParticipant(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      joinedAt: joinedAt ?? this.joinedAt,
      lastSeen: lastSeen ?? this.lastSeen,
      isOnline: isOnline ?? this.isOnline,
      isTyping: isTyping ?? this.isTyping,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      isMuted: isMuted ?? this.isMuted,
    );
  }
}

class Chat {
  final String id;
  final ChatType type;
  final String? title;
  final String? description;
  final String? avatar;
  final List<ChatParticipant> participants;
  final Message? lastMessage;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, int> unreadCounts;
  final bool isActive;
  final String? createdBy;
  final Map<String, dynamic>? metadata;

  const Chat({
    required this.id,
    required this.type,
    this.title,
    this.description,
    this.avatar,
    required this.participants,
    this.lastMessage,
    required this.createdAt,
    required this.updatedAt,
    required this.unreadCounts,
    this.isActive = true,
    this.createdBy,
    this.metadata,
  });

  factory Chat.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Chat(
      id: doc.id,
      type: ChatType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => ChatType.direct,
      ),
      title: data['title'],
      description: data['description'],
      avatar: data['avatar'],
      participants:
          (data['participants'] as List<dynamic>?)
              ?.map((p) => ChatParticipant.fromMap(p as Map<String, dynamic>))
              .toList() ??
          [],
      lastMessage: data['lastMessage'] != null
          ? _MessageHelper.fromMap(
              data['lastMessage']['id'] ?? '',
              data['lastMessage'] as Map<String, dynamic>,
            )
          : null,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      unreadCounts: Map<String, int>.from(data['unreadCounts'] ?? {}),
      isActive: data['isActive'] ?? true,
      createdBy: data['createdBy'],
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'type': type.name,
      'title': title,
      'description': description,
      'avatar': avatar,
      'participants': participants.map((p) => p.toMap()).toList(),
      'lastMessage': lastMessage?.toFirestore(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'unreadCounts': unreadCounts,
      'isActive': isActive,
      'createdBy': createdBy,
      'metadata': metadata,
    };
  }

  // Helper methods
  String getDisplayName(String currentUserId) {
    if (type == ChatType.group) {
      return title ?? 'Group Chat';
    }

    final otherParticipant = participants.firstWhere(
      (p) => p.userId != currentUserId,
      orElse: () => participants.first,
    );
    return otherParticipant.name;
  }

  String? getDisplayAvatar(String currentUserId) {
    if (type == ChatType.group) {
      return avatar;
    }

    final otherParticipant = participants.firstWhere(
      (p) => p.userId != currentUserId,
      orElse: () => participants.first,
    );
    return otherParticipant.avatar;
  }

  bool isUserOnline(String userId) {
    return participants.any((p) => p.userId == userId && p.isOnline);
  }

  bool isUserTyping(String userId) {
    return participants.any((p) => p.userId == userId && p.isTyping);
  }

  int getUnreadCount(String userId) {
    return unreadCounts[userId] ?? 0;
  }

  bool hasUnreadMessages(String userId) {
    return getUnreadCount(userId) > 0;
  }

  Chat copyWith({
    String? id,
    ChatType? type,
    String? title,
    String? description,
    String? avatar,
    List<ChatParticipant>? participants,
    Message? lastMessage,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, int>? unreadCounts,
    bool? isActive,
    String? createdBy,
    Map<String, dynamic>? metadata,
  }) {
    return Chat(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      avatar: avatar ?? this.avatar,
      participants: participants ?? this.participants,
      lastMessage: lastMessage ?? this.lastMessage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      unreadCounts: unreadCounts ?? this.unreadCounts,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Chat && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Helper class for creating message from map data
class _MessageHelper {
  static Message fromMap(String id, Map<String, dynamic> data) {
    return Message(
      id: id,
      chatId: data['chatId'] ?? '',
      senderId: data['senderId'] ?? '',
      senderName: data['senderName'] ?? '',
      senderAvatar: data['senderAvatar'],
      content: data['content'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => MessageType.text,
      ),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => MessageStatus.sent,
      ),
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      readAt: (data['readAt'] as Timestamp?)?.toDate(),
      imageUrl: data['imageUrl'],
      fileName: data['fileName'],
      fileSize: data['fileSize'],
      metadata: data['metadata'],
    );
  }
}
