### App Overview
**dekuMArt** is a Flutter-based community marketplace app tailored for Dedan <PERSON> University students, faculty, and staff. It facilitates buying, selling, and trading items within the university community, while incorporating social features like chats and community forums. The app emphasizes a clean, intuitive UI with university-themed branding (e.g., green and white colors inspired by DeKUT's logo). Key goals:
- Promote campus sustainability through second-hand trading.
- Foster community engagement via forums and direct messaging.
- Ensure secure, university-verified user accounts (e.g., via DeKUT email login).

**Target Platforms**: Android and iOS (Flutter's cross-platform capability).
**Tech Stack**:
- Framework: Flutter 3.x+.
- State Management: Provider or Riverpod for simplicity and scalability.
- Backend: Firebase (Authentication, Firestore for data, Storage for images, Cloud Messaging for notifications) – easy integration for a university app.
- Additional Packages: flutter_bloc (optional for complex state), firebase_auth, cloud_firestore, image_picker (for adding products), intl (for date formatting), http (for any external APIs if needed).
- Authentication: Google Sign-In or email/password, restricted to @dkut.ac.ke domains.
- Database Structure: Users (profiles), Products (listings with images, price, category), Chats (one-to-one messaging), Communities (posts, threads).

**Monetization/Features**: Free app with optional premium features (e.g., boosted listings). Include safety tips for in-person meetups on campus.

### Navigation and UI Structure
The app uses a single `Scaffold` as the root widget for the main screen, with a persistent `BottomNavigationBar` for tab switching. The `AppBar` is customized per tab where needed, but generally includes right-aligned actions. Navigation is handled via `Navigator` with named routes for deep linking.

#### 1. Bottom Tab Bar
Implemented using `BottomNavigationBar` with 5 items. Each tab corresponds to a `Page` or `Widget` stacked via `IndexedStack` to preserve state on tab switches (better performance than rebuilding).
- **Icons and Labels** (use Material Icons or custom SVGs):
  - Home: Icons.home, label: "Home"
  - Products: Icons.shopping_bag, label: "Products"
  - Add: Icons.add_circle, label: "Add" (no navigation; triggers bottom sheet)
  - Chats: Icons.chat_bubble, label: "Chats"
  - Community: Icons.group, label: "Community"
- **Behavior**: 
  - Tapping a tab switches the body content.
  - Current tab highlighted with DeKUT green accent.
  - For "Add" tab: Instead of changing the body, show a `ModalBottomSheet` with options (e.g., "Post Product", "Create Community Post", "Start Chat"). This keeps the current tab's context intact.

#### 2. App Bar
Custom `AppBar` widget, persistent across tabs (or tab-specific if needed via conditional rendering).
- **Left Side**: App logo ("dekuMArt") or back button if in sub-screens.
- **Center**: Tab-specific title (e.g., "Home", "Products").
- **Right Side Actions** (trailing icons, in order):
  - Search: Icons.search – Taps to open a full-screen search overlay or navigate to a search page (filters products/communities by keywords, categories, price).
  - Cart: Icons.shopping_cart – Shows user's saved/favorited items or ongoing purchases. Badge for item count.
  - Profile: Icons.person – Taps to open the right-end drawer (endDrawer in Scaffold).
- **Style**: Elevation 0 for modern look, background color white with green icons.

#### 3. Right-End Drawer (Profile Drawer)
Implemented as `endDrawer` in Scaffold, sliding from the right (like Reddit's profile menu).
- **Trigger**: Only via the profile icon in AppBar (not swipe gesture to avoid conflicts).
- **Content**:
  - Header: User avatar, name, DeKUT ID/role (student/faculty), bio snippet.
  - ListTiles for:
    - My Profile: Edit details, view listings.
    - Settings: Notifications, theme (light/dark), privacy.
    - Wallet/Transactions: View purchase history, balances if payments integrated (e.g., via MPESA API).
    - Help & Support: FAQ, report issues.
    - Logout: Signs out and redirects to login.
- **Width**: 80% of screen width for mobile-friendliness.
- **Animation**: Default Flutter drawer animation.

### Screen Breakdown by Tab
Each tab's body is a separate widget, wrapped in a `SafeArea` for notch handling.

#### Home Tab
- **Purpose**: Dashboard for personalized content.
- **UI Components**:
  - Top: Carousel slider for featured listings (e.g., "Hot Deals on Campus").
  - Sections: 
    - Recommended Products (GridView of cards with image, title, price).
    - Recent Community Posts (ListView snippets).
    - University Announcements (fetched from Firestore or integrated DeKUT API).
  - Refresh: Pull-to-refresh with RefreshIndicator.
- **Features**: Personalized feeds based on user interests (e.g., electronics, books).

#### Products Tab
- **Purpose**: Marketplace browsing and listing.
- **UI Components**:
  - GridView or ListView of product cards (image, title, price, seller, location on campus).
  - Filters: Dropdowns for categories (e.g., Books, Electronics, Furniture), sort by price/date.
  - Infinite scroll with pagination (Firestore queries with limit/offset).
- **Features**: Tap card to view details (full images, description, contact seller button leading to Chats).

#### Add "Tab" (Bottom Sheet)
- **Purpose**: Quick actions without leaving current screen.
- **UI**: ModalBottomSheet with rounded corners, draggable.
  - Options as ListTiles:
    - "Post a Product": Opens form (title, description, price, category, image upload via ImagePicker).
    - "Create Community Post": Text editor with image attachments.
    - "Start a New Chat": Search users to message.
    - "Other": e.g., Report issue.
- **Behavior**: After action, close sheet and refresh relevant tab if needed (e.g., post product refreshes Products tab).

#### Chats Tab
- **Purpose**: Messaging for buyers/sellers.
- **UI Components**:
  - ListView of chat threads (user avatar, last message, timestamp, unread badge).
  - Search bar at top to find users.
- **Features**: Real-time messaging via Firestore listeners. Include image sharing, read receipts. Integrate notifications for new messages.

#### Community Tab
- **Purpose**: Social forums for discussions (e.g., buy/sell tips, campus events).
- **UI Components**:
  - Top TabBar (using TabBar/TabBarView):
    - "For You": Algorithmic feed (based on follows/interests).
    - "Following": Posts from followed users/communities.
    - "All Communities": Directory of groups (e.g., "Engineering Club", "Dorm Deals").
  - Body: ListView of posts (title, content, upvotes, comments).
- **Features**: Post creation via Add sheet. Commenting, upvoting (Firestore counters). Moderation for university-appropriate content.

### Development Plan
#### Phase 1: Setup and Core Structure (1-2 weeks)
- Initialize Flutter project.
- Set up Firebase (auth, database).
- Implement bottom nav, AppBar, and endDrawer.
- Create skeleton pages for each tab.

#### Phase 2: Feature Implementation (3-4 weeks)
- Home: Carousel and sections.
- Products: Listings with Firestore integration.
- Add: Bottom sheet and forms.
- Chats: Real-time messaging.
- Community: Top tabs and feeds.

#### Phase 3: Polish and Testing (1-2 weeks)
- Add search, cart functionality.
- UI theming, responsiveness (use MediaQuery for layouts).
- Testing: Unit tests for providers, integration tests for navigation, device testing on Android/iOS emulators.
- Security: Validate inputs, handle errors (e.g., no internet).

#### Phase 4: Deployment
- Beta release via Firebase App Distribution for DeKUT students.
- App Store/Play Store submission.
- Analytics: Integrate Firebase Analytics for usage tracking.

**Potential Challenges**:
- Real-time updates: Use StreamBuilders for Firestore.
- Image Handling: Compress uploads to save storage.
- Scalability: Index Firestore queries for large user base.

This plan provides a solid foundation; iterate based on user feedback from DeKUT community.