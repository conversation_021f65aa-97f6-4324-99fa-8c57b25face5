# 🔧 Cloudinary Upload Error Fix Guide

## 🚨 Current Issue
**Error**: "failed to upload image 1: upload failed with status: 400"

This error indicates that the Cloudinary upload preset is not configured correctly or doesn't exist.

## 🎯 Quick Fix Steps

### Step 1: Test Your Configuration
1. Add this import to your main app file or any test screen:
```dart
import 'lib/screens/cloudinary_test_screen.dart';
```

2. Navigate to the CloudinaryTestScreen to test your configuration
3. Click "Test Upload Preset" to see detailed error information

### Step 2: Fix Upload Preset (Most Likely Issue)

#### Go to Cloudinary Dashboard
1. Visit [cloudinary.com](https://cloudinary.com) and log in
2. Go to **Settings** > **Upload**
3. Look for an upload preset named `dekumart_products`

#### If Preset Doesn't Exist - Create It:
1. Click **"Add upload preset"**
2. Fill in these settings:
   - **Preset name**: `dekumart_products`
   - **Signing mode**: `Unsigned` ⚠️ **IMPORTANT**
   - **Folder**: `dekumart/products`
   - **Allowed formats**: `jpg, png, jpeg, webp`
   - **Max file size**: `10MB`
   - **Auto-optimization**: `Enabled`
3. Click **"Save"**

#### If Preset Exists - Check Settings:
1. Click on the `dekumart_products` preset
2. Verify these settings:
   - ✅ **Signing mode**: Must be `Unsigned`
   - ✅ **Folder**: `dekumart/products` (optional)
   - ✅ **Allowed formats**: Include `jpg, png, jpeg, webp`
   - ✅ **Max file size**: At least `10MB`
3. Save any changes

### Step 3: Verify Cloud Name
Check that your cloud name in `lib/config/cloudinary_config.dart` matches your dashboard:

```dart
static const String cloudName = 'dm5ifbsct'; // Should match your dashboard
```

To find your cloud name:
1. Go to Cloudinary Dashboard
2. Look at the top-left corner for "Cloud name: your-cloud-name"

## 🔍 Common Issues & Solutions

### Issue 1: Preset is "Signed" instead of "Unsigned"
**Solution**: Change the preset to "Unsigned" mode
- Signed uploads require API secret and signature
- Unsigned uploads only need the preset name

### Issue 2: Preset Name Mismatch
**Solution**: Ensure preset name is exactly `dekumart_products`
- Case sensitive
- No spaces or special characters

### Issue 3: Cloud Name Incorrect
**Solution**: Copy exact cloud name from dashboard
- Found in dashboard URL: `https://console.cloudinary.com/console/c-{cloud-name}`

### Issue 4: File Format Restrictions
**Solution**: Allow image formats in preset settings
- jpg, jpeg, png, webp should be allowed

### Issue 5: File Size Limits
**Solution**: Set appropriate file size limits
- Recommended: 10MB maximum

## 🧪 Testing Your Fix

### Method 1: Use Test Screen
1. Navigate to CloudinaryTestScreen
2. Click "Test Upload Preset"
3. Check if test passes

### Method 2: Try Product Upload
1. Go to "Post Product" screen
2. Add a small image (< 1MB)
3. Try to submit the product
4. Check if upload succeeds

## 🔧 Alternative Quick Fix

If you can't access Cloudinary dashboard right now, try this temporary fix:

### Create a Simple Unsigned Preset
1. Go to Cloudinary Dashboard
2. Settings > Upload
3. Create new preset with minimal settings:
   - Name: `dekumart_products`
   - Mode: `Unsigned`
   - Save

This should work immediately for basic uploads.

## 📋 Verification Checklist

After making changes, verify:
- [ ] Upload preset `dekumart_products` exists
- [ ] Preset is set to "Unsigned" mode
- [ ] Cloud name matches dashboard
- [ ] Image formats are allowed (jpg, png, jpeg, webp)
- [ ] File size limit is appropriate (10MB+)
- [ ] Test upload works in CloudinaryTestScreen
- [ ] Product image upload works in app

## 🆘 Still Having Issues?

### Check Network/Firewall
- Ensure app can reach `api.cloudinary.com`
- Check if corporate firewall blocks uploads

### Verify Credentials
- Double-check cloud name spelling
- Ensure no extra spaces in configuration

### Try Different Image
- Test with a very small image (< 100KB)
- Try different image formats (JPG vs PNG)

### Check Cloudinary Logs
1. Go to Cloudinary Dashboard
2. Navigate to "Reports" > "Usage"
3. Look for failed upload attempts
4. Check error messages

## 🎯 Expected Result

After fixing the upload preset, you should see:
- ✅ CloudinaryTestScreen shows "Upload preset is working correctly"
- ✅ Product images upload successfully
- ✅ Images appear in your Cloudinary Media Library
- ✅ No more 400 status errors

## 📞 Need Help?

If you're still having issues:
1. Run the CloudinaryTestScreen and share the detailed error output
2. Check your Cloudinary dashboard for any error logs
3. Verify your internet connection allows uploads to cloudinary.com

The most common cause is the upload preset not being set to "Unsigned" mode. Fix this first!
