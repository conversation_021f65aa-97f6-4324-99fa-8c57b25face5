class CloudinaryConfig {
  // Replace these with your actual Cloudinary credentials
  static const String cloudName = 'dm5ifbsct';
  static const String apiKey = '459965622351224';
  static const String apiSecret = 'ZS20arklJUeFwkyo152pxjjnTqY';
  static const String uploadPreset = 'dekumart_products';

  // Upload settings
  static const int maxImageSize = 1024; // Max width/height in pixels
  static const int imageQuality = 85; // Image quality (0-100)
  static const String defaultFolder = 'dekumart/products';

  // Transformation presets
  static const String thumbnailTransform =
      'w_300,h_300,c_fill,q_auto:good,f_auto';
  static const String mediumTransform = 'w_600,h_600,c_fit,q_auto:good,f_auto';
  static const String largeTransform = 'w_1200,h_1200,c_fit,q_auto:good,f_auto';

  // Get optimized image URL
  static String getOptimizedUrl(String publicId, {String? transform}) {
    final transformString = transform ?? thumbnailTransform;
    return 'https://res.cloudinary.com/$cloudName/image/upload/$transformString/$publicId';
  }

  // Get different sizes
  static String getThumbnailUrl(String publicId) =>
      getOptimizedUrl(publicId, transform: thumbnailTransform);

  static String getMediumUrl(String publicId) =>
      getOptimizedUrl(publicId, transform: mediumTransform);

  static String getLargeUrl(String publicId) =>
      getOptimizedUrl(publicId, transform: largeTransform);
}

// Instructions for setting up Cloudinary:
/*
1. Sign up for a free Cloudinary account at https://cloudinary.com/
2. Go to your Dashboard and copy your Cloud Name, API Key, and API Secret
3. Create an upload preset:
   - Go to Settings > Upload
   - Click "Add upload preset"
   - Set preset name to "dekumart_products"
   - Set signing mode to "Unsigned"
   - Configure folder to "dekumart/products"
   - Set allowed formats to: jpg, png, jpeg, webp
   - Set max file size to 10MB
   - Enable auto-optimization
4. Replace the values above with your actual credentials
5. For production, consider using environment variables or secure storage
*/
