import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/wishlist_item.dart';
import '../services/firebase_service.dart';

class WishlistService {
  final FirebaseService _firebaseService = FirebaseService();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get current user's wishlist collection reference
  CollectionReference get _userWishlistCollection {
    final userId = _auth.currentUser?.uid;
    if (userId == null) {
      throw Exception('User not authenticated');
    }
    return _firebaseService.firestore
        .collection('users')
        .doc(userId)
        .collection('wishlist');
  }

  /// Add item to wishlist
  Future<void> addToWishlist(WishlistItem item) async {
    try {
      await _userWishlistCollection.doc(item.id).set(item.toFirestore());
    } catch (e) {
      throw Exception('Failed to add item to wishlist: $e');
    }
  }

  /// Get all wishlist items for current user
  Future<List<WishlistItem>> getWishlistItems() async {
    try {
      final querySnapshot = await _userWishlistCollection
          .orderBy('addedAt', descending: true)
          .get();
      
      return querySnapshot.docs
          .map((doc) => WishlistItem.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get wishlist items: $e');
    }
  }

  /// Get wishlist items stream for real-time updates
  Stream<List<WishlistItem>> getWishlistItemsStream() {
    try {
      return _userWishlistCollection
          .orderBy('addedAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => WishlistItem.fromFirestore(doc))
              .toList());
    } catch (e) {
      throw Exception('Failed to get wishlist items stream: $e');
    }
  }

  /// Update wishlist item
  Future<void> updateWishlistItem(WishlistItem item) async {
    try {
      await _userWishlistCollection.doc(item.id).update(item.toFirestore());
    } catch (e) {
      throw Exception('Failed to update wishlist item: $e');
    }
  }

  /// Remove item from wishlist
  Future<void> removeFromWishlist(String itemId) async {
    try {
      await _userWishlistCollection.doc(itemId).delete();
    } catch (e) {
      throw Exception('Failed to remove item from wishlist: $e');
    }
  }

  /// Clear entire wishlist
  Future<void> clearWishlist() async {
    try {
      final batch = _firebaseService.firestore.batch();
      final querySnapshot = await _userWishlistCollection.get();
      
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
    } catch (e) {
      throw Exception('Failed to clear wishlist: $e');
    }
  }

  /// Get wishlist item by product ID
  Future<WishlistItem?> getWishlistItemByProductId(String productId) async {
    try {
      final querySnapshot = await _userWishlistCollection
          .where('productId', isEqualTo: productId)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isNotEmpty) {
        return WishlistItem.fromFirestore(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get wishlist item: $e');
    }
  }

  /// Check if product is in wishlist
  Future<bool> isProductInWishlist(String productId) async {
    try {
      final item = await getWishlistItemByProductId(productId);
      return item != null;
    } catch (e) {
      return false;
    }
  }

  /// Get wishlist summary
  Future<Map<String, dynamic>> getWishlistSummary() async {
    try {
      final items = await getWishlistItems();
      
      final totalItems = items.length;
      final availableItems = items.where((item) => item.isAvailable).length;
      final unavailableItems = totalItems - availableItems;
      final totalValue = items
          .where((item) => item.isAvailable)
          .fold<double>(0.0, (sum, item) => sum + item.product.price);
      
      return {
        'totalItems': totalItems,
        'availableItems': availableItems,
        'unavailableItems': unavailableItems,
        'totalValue': totalValue,
      };
    } catch (e) {
      throw Exception('Failed to get wishlist summary: $e');
    }
  }

  /// Sync wishlist with updated product information
  Future<void> syncWishlistWithProducts() async {
    try {
      final items = await getWishlistItems();
      final batch = _firebaseService.firestore.batch();
      
      for (final item in items) {
        // Get updated product information
        final productDoc = await _firebaseService.productsCollection
            .doc(item.productId)
            .get();
        
        if (productDoc.exists) {
          final updatedProductData = productDoc.data() as Map<String, dynamic>;
          
          // Create updated product object
          final updatedProduct = item.product.copyWith(
            price: (updatedProductData['price'] ?? item.product.price).toDouble(),
            stock: updatedProductData['stock'] ?? item.product.stock,
            isActive: updatedProductData['isActive'] ?? item.product.isActive,
          );
          
          // Update wishlist item with latest product info
          final updatedWishlistItem = item.copyWith(product: updatedProduct);
          
          batch.update(
            _userWishlistCollection.doc(item.id),
            updatedWishlistItem.toFirestore(),
          );
        } else {
          // Product no longer exists, remove from wishlist
          batch.delete(_userWishlistCollection.doc(item.id));
        }
      }
      
      await batch.commit();
    } catch (e) {
      throw Exception('Failed to sync wishlist with products: $e');
    }
  }

  /// Get price drop notifications (items where price has decreased)
  Future<List<WishlistItem>> getPriceDropItems() async {
    try {
      final items = await getWishlistItems();
      final priceDropItems = <WishlistItem>[];
      
      for (final item in items) {
        final productDoc = await _firebaseService.productsCollection
            .doc(item.productId)
            .get();
        
        if (productDoc.exists) {
          final currentPrice = (productDoc.data() as Map<String, dynamic>)['price']?.toDouble() ?? 0.0;
          final originalPrice = item.product.price;
          
          if (currentPrice < originalPrice) {
            priceDropItems.add(item);
          }
        }
      }
      
      return priceDropItems;
    } catch (e) {
      throw Exception('Failed to get price drop items: $e');
    }
  }

  /// Get back in stock items
  Future<List<WishlistItem>> getBackInStockItems() async {
    try {
      final items = await getWishlistItems();
      final backInStockItems = <WishlistItem>[];
      
      for (final item in items) {
        // If item was previously out of stock
        if (item.product.stock == 0) {
          final productDoc = await _firebaseService.productsCollection
              .doc(item.productId)
              .get();
          
          if (productDoc.exists) {
            final currentStock = (productDoc.data() as Map<String, dynamic>)['stock'] ?? 0;
            
            if (currentStock > 0) {
              backInStockItems.add(item);
            }
          }
        }
      }
      
      return backInStockItems;
    } catch (e) {
      throw Exception('Failed to get back in stock items: $e');
    }
  }

  /// Remove items that are no longer available
  Future<void> cleanupUnavailableItems() async {
    try {
      final items = await getWishlistItems();
      final batch = _firebaseService.firestore.batch();
      
      for (final item in items) {
        final productDoc = await _firebaseService.productsCollection
            .doc(item.productId)
            .get();
        
        if (!productDoc.exists) {
          // Product no longer exists, remove from wishlist
          batch.delete(_userWishlistCollection.doc(item.id));
        } else {
          final productData = productDoc.data() as Map<String, dynamic>;
          final isActive = productData['isActive'] ?? false;
          
          if (!isActive) {
            // Product is inactive, remove from wishlist
            batch.delete(_userWishlistCollection.doc(item.id));
          }
        }
      }
      
      await batch.commit();
    } catch (e) {
      throw Exception('Failed to cleanup unavailable items: $e');
    }
  }
}
