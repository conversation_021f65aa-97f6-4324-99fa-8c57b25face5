import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:intl/intl.dart';

class CommunityTab extends StatefulWidget {
  const CommunityTab({super.key});

  @override
  State<CommunityTab> createState() => _CommunityTabState();
}

class _CommunityTabState extends State<CommunityTab>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  bool _showFab = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.userScrollDirection ==
        ScrollDirection.reverse) {
      if (_showFab) setState(() => _showFab = false);
    } else if (_scrollController.position.userScrollDirection ==
        ScrollDirection.forward) {
      if (!_showFab) setState(() => _showFab = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Column(
          children: [
            // Modern Tab Bar with Reddit/Twitter style
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                ),
              ),
              child: Column(
                children: [
                  // Top section with create post button
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      children: [
                        const CircleAvatar(
                          radius: 16,
                          backgroundColor: Color(0xFF2E7D32),
                          child: Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: GestureDetector(
                            onTap: _showCreatePostDialog,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(25),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: Text(
                                "What's happening at DeKUT?",
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          onPressed: _showCreatePostDialog,
                          icon: const Icon(
                            Icons.add_photo_alternate,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Tab Bar
                  TabBar(
                    controller: _tabController,
                    labelColor: const Color(0xFF2E7D32),
                    unselectedLabelColor: Colors.grey.shade600,
                    indicatorColor: const Color(0xFF2E7D32),
                    indicatorWeight: 3,
                    labelStyle: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 15,
                    ),
                    unselectedLabelStyle: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 15,
                    ),
                    tabs: const [
                      Tab(text: 'Home'),
                      Tab(text: 'Following'),
                      Tab(text: 'Communities'),
                    ],
                  ),
                ],
              ),
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildForYouTab(),
                  _buildFollowingTab(),
                  _buildCommunitiesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: AnimatedSlide(
        duration: const Duration(milliseconds: 300),
        offset: _showFab ? Offset.zero : const Offset(0, 2),
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 300),
          opacity: _showFab ? 1.0 : 0.0,
          child: FloatingActionButton(
            onPressed: _showCreatePostDialog,
            backgroundColor: const Color(0xFF2E7D32),
            child: const Icon(Icons.edit, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildForYouTab() {
    return RefreshIndicator(
      onRefresh: () async {
        // TODO: Implement refresh logic
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.separated(
        controller: _scrollController,
        padding: EdgeInsets.zero,
        itemCount: _generateSamplePosts().length,
        separatorBuilder: (context, index) =>
            Container(height: 8, color: Colors.grey.shade100),
        itemBuilder: (context, index) {
          final post = _generateSamplePosts()[index];
          return _buildModernPostCard(post);
        },
      ),
    );
  }

  Widget _buildFollowingTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 80, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'No posts from followed users',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Follow other users to see their posts here',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildCommunitiesTab() {
    return Container(
      color: Colors.grey.shade50,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: _generateSampleCommunities().length,
        separatorBuilder: (context, index) =>
            Container(height: 1, color: Colors.grey.shade200),
        itemBuilder: (context, index) {
          final community = _generateSampleCommunities()[index];
          return _buildModernCommunityCard(community);
        },
      ),
    );
  }

  Widget _buildModernPostCard(Map<String, dynamic> post) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post Header
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 8, 0),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 18,
                  backgroundColor: const Color(0xFF2E7D32),
                  child: Text(
                    post['author'][0].toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            post['author'],
                            style: const TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 15,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '@${post['author'].toLowerCase().replaceAll(' ', '')}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '• ${_formatPostTime(post['timestamp'])}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      if (post['community'] != null)
                        Text(
                          'in ${post['community']}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 13,
                          ),
                        ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.more_horiz, color: Colors.grey.shade600),
                  onPressed: () => _showPostOptions(post),
                  padding: const EdgeInsets.all(8),
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),

          // Post Content
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (post['title'] != null && post['title'].isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      post['title'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        height: 1.3,
                      ),
                    ),
                  ),
                Text(
                  post['content'],
                  style: const TextStyle(fontSize: 15, height: 1.4),
                ),
              ],
            ),
          ),

          // Post Image (if any)
          if (post['hasImage'])
            Container(
              margin: const EdgeInsets.fromLTRB(16, 12, 16, 0),
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: const Center(
                  child: Icon(Icons.image, size: 50, color: Colors.grey),
                ),
              ),
            ),

          // Post Actions
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 8, 8, 12),
            child: Row(
              children: [
                _buildModernPostAction(
                  icon: post['isUpvoted']
                      ? Icons.arrow_upward
                      : Icons.arrow_upward_outlined,
                  label: _formatCount(post['upvotes']),
                  color: post['isUpvoted']
                      ? const Color(0xFF2E7D32)
                      : Colors.grey.shade600,
                  onTap: () => _toggleUpvote(post),
                ),
                _buildModernPostAction(
                  icon: post['isDownvoted']
                      ? Icons.arrow_downward
                      : Icons.arrow_downward_outlined,
                  label: '',
                  color: post['isDownvoted']
                      ? Colors.red
                      : Colors.grey.shade600,
                  onTap: () => _toggleDownvote(post),
                ),
                const SizedBox(width: 8),
                _buildModernPostAction(
                  icon: Icons.mode_comment_outlined,
                  label: _formatCount(post['comments']),
                  color: Colors.grey.shade600,
                  onTap: () => _openComments(post),
                ),
                const SizedBox(width: 8),
                _buildModernPostAction(
                  icon: Icons.share_outlined,
                  label: '',
                  color: Colors.grey.shade600,
                  onTap: () => _sharePost(post),
                ),
                const Spacer(),
                _buildModernPostAction(
                  icon: post['isSaved']
                      ? Icons.bookmark
                      : Icons.bookmark_border,
                  label: '',
                  color: post['isSaved']
                      ? const Color(0xFF2E7D32)
                      : Colors.grey.shade600,
                  onTap: () => _toggleSave(post),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernPostAction({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 18),
            if (label.isNotEmpty) ...[
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatCount(int count) {
    if (count < 1000) return count.toString();
    if (count < 1000000) return '${(count / 1000).toStringAsFixed(1)}k';
    return '${(count / 1000000).toStringAsFixed(1)}m';
  }

  void _toggleDownvote(Map<String, dynamic> post) {
    setState(() {
      if (post['isDownvoted']) {
        post['downvotes']--;
        post['isDownvoted'] = false;
      } else {
        if (post['isUpvoted']) {
          post['upvotes']--;
          post['isUpvoted'] = false;
        }
        post['downvotes']++;
        post['isDownvoted'] = true;
      }
    });
  }

  void _showCreatePostDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                  const Spacer(),
                  const Text(
                    'Create Post',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                  const Spacer(),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Post created!')),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7D32),
                      foregroundColor: Colors.white,
                      elevation: 0,
                    ),
                    child: const Text('Post'),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const CircleAvatar(
                          radius: 20,
                          backgroundColor: Color(0xFF2E7D32),
                          child: Icon(Icons.person, color: Colors.white),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            children: [
                              TextField(
                                decoration: const InputDecoration(
                                  hintText: "What's happening at DeKUT?",
                                  border: InputBorder.none,
                                  hintStyle: TextStyle(fontSize: 18),
                                ),
                                style: const TextStyle(fontSize: 18),
                                maxLines: null,
                                autofocus: true,
                              ),
                              const SizedBox(height: 20),
                              Container(
                                height: 200,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add_photo_alternate,
                                      size: 48,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Add photos',
                                      style: TextStyle(
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernCommunityCard(Map<String, dynamic> community) {
    return Container(
      color: Colors.white,
      child: InkWell(
        onTap: () => _openCommunity(community),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Community Avatar
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: community['color'],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(community['icon'], color: Colors.white, size: 24),
              ),
              const SizedBox(width: 16),
              // Community Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      community['name'],
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      community['description'],
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.people,
                          size: 14,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_formatCount(community['members'])} members',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.article,
                          size: 14,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_formatCount(community['posts'])} posts',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Join Button
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: community['isJoined']
                      ? Colors.grey.shade200
                      : const Color(0xFF2E7D32),
                  borderRadius: BorderRadius.circular(20),
                  border: community['isJoined']
                      ? Border.all(color: Colors.grey.shade400)
                      : null,
                ),
                child: Text(
                  community['isJoined'] ? 'Joined' : 'Join',
                  style: TextStyle(
                    color: community['isJoined']
                        ? Colors.grey.shade700
                        : Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _generateSamplePosts() {
    return [
      {
        'id': '1',
        'author': 'John Doe',
        'title': '',
        'content':
            'Anyone interested in forming a study group for Engineering Mathematics? We can meet at the library every Tuesday and Thursday. 📚',
        'community': 'r/DeKUTStudents',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'upvotes': 1247,
        'downvotes': 23,
        'comments': 89,
        'isUpvoted': false,
        'isDownvoted': false,
        'isSaved': false,
        'hasImage': false,
      },
      {
        'id': '2',
        'author': 'Jane Smith',
        'title': '',
        'content':
            'I have some extra lab equipment that I no longer need. First come, first served! Contact me if interested. DM for details 🔬⚗️',
        'community': 'r/DeKUTMarketplace',
        'timestamp': DateTime.now().subtract(const Duration(hours: 5)),
        'upvotes': 2341,
        'downvotes': 45,
        'comments': 156,
        'isUpvoted': true,
        'isDownvoted': false,
        'isSaved': true,
        'hasImage': true,
      },
      {
        'id': '3',
        'author': 'Mike Johnson',
        'title': '',
        'content':
            'Don\'t miss the tech talk tomorrow at 2 PM in the main auditorium. Industry experts will be discussing the latest trends in AI and machine learning. Free pizza! 🍕🤖',
        'community': 'r/DeKUTEvents',
        'timestamp': DateTime.now().subtract(const Duration(days: 1)),
        'upvotes': 4567,
        'downvotes': 89,
        'comments': 234,
        'isUpvoted': false,
        'isDownvoted': false,
        'isSaved': false,
        'hasImage': false,
      },
      {
        'id': '4',
        'author': 'Sarah Wilson',
        'title': '',
        'content':
            'Just finished my final project presentation! 🎉 Thanks to everyone who helped me along the way. This community is amazing! #DeKUTLife',
        'community': 'r/DeKUTStudents',
        'timestamp': DateTime.now().subtract(const Duration(hours: 8)),
        'upvotes': 892,
        'downvotes': 12,
        'comments': 67,
        'isUpvoted': false,
        'isDownvoted': false,
        'isSaved': false,
        'hasImage': true,
      },
      {
        'id': '5',
        'author': 'Alex Chen',
        'title': '',
        'content':
            'Looking for a roommate for next semester. Clean, quiet, and responsible. Preferably someone in Engineering or Computer Science. Rent is 15k per month. 🏠',
        'community': 'r/DeKUTHousing',
        'timestamp': DateTime.now().subtract(const Duration(hours: 12)),
        'upvotes': 156,
        'downvotes': 8,
        'comments': 43,
        'isUpvoted': false,
        'isDownvoted': false,
        'isSaved': true,
        'hasImage': false,
      },
    ];
  }

  List<Map<String, dynamic>> _generateSampleCommunities() {
    return [
      {
        'id': '1',
        'name': 'r/DeKUTStudents',
        'description':
            'The official community for DeKUT students. Share experiences, ask questions, and connect with fellow students.',
        'members': 12450,
        'posts': 2890,
        'icon': Icons.school,
        'color': const Color(0xFF2E7D32),
        'isJoined': true,
      },
      {
        'id': '2',
        'name': 'r/DeKUTMarketplace',
        'description':
            'Buy, sell, and trade items within the DeKUT community. From textbooks to electronics.',
        'members': 8760,
        'posts': 5670,
        'icon': Icons.shopping_bag,
        'color': Colors.orange,
        'isJoined': false,
      },
      {
        'id': '3',
        'name': 'r/DeKUTEngineering',
        'description':
            'For engineering students to share projects, collaborate, and discuss technical topics.',
        'members': 5890,
        'posts': 1230,
        'icon': Icons.engineering,
        'color': Colors.blue,
        'isJoined': true,
      },
      {
        'id': '4',
        'name': 'r/DeKUTEvents',
        'description':
            'Stay updated on campus events, parties, and social gatherings.',
        'members': 9870,
        'posts': 890,
        'icon': Icons.event,
        'color': Colors.purple,
        'isJoined': false,
      },
      {
        'id': '5',
        'name': 'r/DeKUTHousing',
        'description':
            'Find roommates, housing options, and discuss dorm life.',
        'members': 4560,
        'posts': 670,
        'icon': Icons.home,
        'color': Colors.teal,
        'isJoined': true,
      },
      {
        'id': '6',
        'name': 'r/DeKUTSports',
        'description':
            'Organize sports activities, discuss games, and find teammates.',
        'members': 3210,
        'posts': 450,
        'icon': Icons.sports_basketball,
        'color': Colors.red,
        'isJoined': false,
      },
      {
        'id': '7',
        'name': 'r/DeKUTMemes',
        'description': 'Share funny memes and jokes about campus life.',
        'members': 15670,
        'posts': 8900,
        'icon': Icons.emoji_emotions,
        'color': Colors.amber,
        'isJoined': true,
      },
    ];
  }

  String _formatPostTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM dd').format(time);
    }
  }

  void _toggleUpvote(Map<String, dynamic> post) {
    setState(() {
      if (post['isUpvoted']) {
        post['upvotes']--;
        post['isUpvoted'] = false;
      } else {
        if (post['isDownvoted']) {
          post['downvotes']--;
          post['isDownvoted'] = false;
        }
        post['upvotes']++;
        post['isUpvoted'] = true;
      }
    });
  }

  void _toggleSave(Map<String, dynamic> post) {
    setState(() {
      post['isSaved'] = !post['isSaved'];
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(post['isSaved'] ? 'Post saved' : 'Post unsaved'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _openComments(Map<String, dynamic> post) {
    // TODO: Navigate to comments screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Comments coming soon!'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _sharePost(Map<String, dynamic> post) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon!'),
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _showPostOptions(Map<String, dynamic> post) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.flag),
              title: const Text('Report Post'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Report post
              },
            ),
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Hide Post'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Hide post
              },
            ),
          ],
        ),
      ),
    );
  }

  void _joinCommunity(Map<String, dynamic> community) {
    setState(() {
      community['isJoined'] = !community['isJoined'];
      if (community['isJoined']) {
        community['members']++;
      } else {
        community['members']--;
      }
    });
  }

  void _openCommunity(Map<String, dynamic> community) {
    // TODO: Navigate to community details
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${community['name']}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}
