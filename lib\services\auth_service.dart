import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_service.dart';

/// Authentication service for dekuMArt app
/// <PERSON>les user authentication with DeKUT email restriction
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseService _firebaseService = FirebaseService();
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  // DeKUT email domain for validation
  static const String dekutDomain = '@dkut.ac.ke';

  /// Sign in with email and password
  Future<UserCredential?> signInWithEmailPassword(String email, String password) async {
    try {
      // Validate DeKUT email domain
      if (!_isValidDekutEmail(email)) {
        throw FirebaseAuthException(
          code: 'invalid-email-domain',
          message: 'Please use your DeKUT email address (@dkut.ac.ke)',
        );
      }

      UserCredential userCredential = await _firebaseService.auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      await _updateUserLastSeen(userCredential.user);
      return userCredential;
    } on FirebaseAuthException catch (e) {
      print('Sign in error: ${e.message}');
      rethrow;
    }
  }

  /// Register with email and password
  Future<UserCredential?> registerWithEmailPassword(
    String email,
    String password,
    String fullName,
    String studentId,
  ) async {
    try {
      // Validate DeKUT email domain
      if (!_isValidDekutEmail(email)) {
        throw FirebaseAuthException(
          code: 'invalid-email-domain',
          message: 'Please use your DeKUT email address (@dkut.ac.ke)',
        );
      }

      UserCredential userCredential = await _firebaseService.auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update user profile
      await userCredential.user?.updateDisplayName(fullName);

      // Create user document in Firestore
      await _createUserDocument(userCredential.user!, fullName, studentId);

      return userCredential;
    } on FirebaseAuthException catch (e) {
      print('Registration error: ${e.message}');
      rethrow;
    }
  }

  /// Sign in with Google (restricted to DeKUT domain)
  Future<UserCredential?> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) return null;

      // Validate DeKUT email domain
      if (!_isValidDekutEmail(googleUser.email)) {
        await _googleSignIn.signOut();
        throw FirebaseAuthException(
          code: 'invalid-email-domain',
          message: 'Please use your DeKUT Google account (@dkut.ac.ke)',
        );
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      UserCredential userCredential = await _firebaseService.auth.signInWithCredential(credential);

      // Create or update user document
      await _createOrUpdateUserDocument(userCredential.user!);

      return userCredential;
    } catch (e) {
      print('Google sign in error: $e');
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await Future.wait([
        _firebaseService.auth.signOut(),
        _googleSignIn.signOut(),
      ]);
    } catch (e) {
      print('Sign out error: $e');
      rethrow;
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      if (!_isValidDekutEmail(email)) {
        throw FirebaseAuthException(
          code: 'invalid-email-domain',
          message: 'Please use your DeKUT email address (@dkut.ac.ke)',
        );
      }

      await _firebaseService.auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      print('Password reset error: $e');
      rethrow;
    }
  }

  /// Validate DeKUT email domain
  bool _isValidDekutEmail(String email) {
    return email.toLowerCase().endsWith(dekutDomain);
  }

  /// Create user document in Firestore
  Future<void> _createUserDocument(User user, String fullName, String studentId) async {
    try {
      await _firebaseService.usersCollection.doc(user.uid).set({
        'uid': user.uid,
        'email': user.email,
        'fullName': fullName,
        'studentId': studentId,
        'photoURL': user.photoURL,
        'createdAt': FieldValue.serverTimestamp(),
        'lastSeen': FieldValue.serverTimestamp(),
        'isActive': true,
        'role': 'student', // Default role
        'bio': '',
        'phone': '',
        'location': 'DeKUT Campus',
        'rating': 0.0,
        'totalRatings': 0,
        'totalSales': 0,
        'totalPurchases': 0,
      });
    } catch (e) {
      print('Error creating user document: $e');
      rethrow;
    }
  }

  /// Create or update user document for Google sign-in
  Future<void> _createOrUpdateUserDocument(User user) async {
    try {
      DocumentSnapshot userDoc = await _firebaseService.usersCollection.doc(user.uid).get();

      if (!userDoc.exists) {
        // Extract student ID from email (before @dkut.ac.ke)
        String studentId = user.email?.split('@')[0] ?? '';

        await _createUserDocument(user, user.displayName ?? '', studentId);
      } else {
        // Update last seen
        await _updateUserLastSeen(user);
      }
    } catch (e) {
      print('Error creating/updating user document: $e');
      rethrow;
    }
  }

  /// Update user last seen timestamp
  Future<void> _updateUserLastSeen(User? user) async {
    if (user != null) {
      try {
        await _firebaseService.usersCollection.doc(user.uid).update({
          'lastSeen': FieldValue.serverTimestamp(),
          'isActive': true,
        });
      } catch (e) {
        print('Error updating last seen: $e');
      }
    }
  }

  /// Get current user data from Firestore
  Future<DocumentSnapshot?> getCurrentUserData() async {
    User? user = _firebaseService.currentUser;
    if (user != null) {
      return await _firebaseService.usersCollection.doc(user.uid).get();
    }
    return null;
  }

  /// Get user data stream
  Stream<DocumentSnapshot> getUserDataStream(String uid) {
    return _firebaseService.usersCollection.doc(uid).snapshots();
  }
}
