import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/review.dart';
import '../services/firebase_service.dart';

/// Service class for handling review-related database operations
class ReviewService {
  static final ReviewService _instance = ReviewService._internal();
  factory ReviewService() => _instance;
  ReviewService._internal();

  final FirebaseService _firebaseService = FirebaseService();

  /// Get reviews collection reference
  CollectionReference get reviewsCollection => 
      _firebaseService.firestore.collection('reviews');

  /// Create a new review
  Future<String> createReview(Review review) async {
    try {
      final docRef = await reviewsCollection.add(review.toFirestore());
      
      // Update product rating after adding review
      await _updateProductRating(review.productId);
      
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create review: $e');
    }
  }

  /// Get a review by ID
  Future<Review?> getReview(String reviewId) async {
    try {
      final doc = await reviewsCollection.doc(reviewId).get();
      if (doc.exists) {
        return Review.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get review: $e');
    }
  }

  /// Get reviews for a specific product
  Future<List<Review>> getProductReviews(
    String productId, {
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = reviewsCollection
          .where('productId', isEqualTo: productId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Review.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get product reviews: $e');
    }
  }

  /// Get reviews by a specific user
  Future<List<Review>> getUserReviews(
    String userId, {
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = reviewsCollection
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => Review.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user reviews: $e');
    }
  }

  /// Update a review
  Future<void> updateReview(
    String reviewId,
    Map<String, dynamic> updates,
  ) async {
    try {
      updates['updatedAt'] = Timestamp.fromDate(DateTime.now());
      await reviewsCollection.doc(reviewId).update(updates);
    } catch (e) {
      throw Exception('Failed to update review: $e');
    }
  }

  /// Delete a review (soft delete)
  Future<void> deleteReview(String reviewId) async {
    try {
      await reviewsCollection.doc(reviewId).update({
        'isActive': false,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to delete review: $e');
    }
  }

  /// Mark review as helpful
  Future<void> markReviewHelpful(String reviewId, String userId) async {
    try {
      await reviewsCollection.doc(reviewId).update({
        'helpfulCount': FieldValue.increment(1),
        'helpfulUserIds': FieldValue.arrayUnion([userId]),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to mark review as helpful: $e');
    }
  }

  /// Unmark review as helpful
  Future<void> unmarkReviewHelpful(String reviewId, String userId) async {
    try {
      await reviewsCollection.doc(reviewId).update({
        'helpfulCount': FieldValue.increment(-1),
        'helpfulUserIds': FieldValue.arrayRemove([userId]),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to unmark review as helpful: $e');
    }
  }

  /// Get review statistics for a product
  Future<Map<String, dynamic>> getProductReviewStats(String productId) async {
    try {
      final querySnapshot = await reviewsCollection
          .where('productId', isEqualTo: productId)
          .where('isActive', isEqualTo: true)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return {
          'totalReviews': 0,
          'averageRating': 0.0,
          'ratingDistribution': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
        };
      }

      final reviews = querySnapshot.docs
          .map((doc) => Review.fromFirestore(doc))
          .toList();

      final totalReviews = reviews.length;
      final totalRating = reviews.fold<double>(
        0.0,
        (sum, review) => sum + review.rating,
      );
      final averageRating = totalRating / totalReviews;

      final ratingDistribution = <int, int>{1: 0, 2: 0, 3: 0, 4: 0, 5: 0};
      for (final review in reviews) {
        final rating = review.rating.round();
        ratingDistribution[rating] = (ratingDistribution[rating] ?? 0) + 1;
      }

      return {
        'totalReviews': totalReviews,
        'averageRating': averageRating,
        'ratingDistribution': ratingDistribution,
      };
    } catch (e) {
      throw Exception('Failed to get review statistics: $e');
    }
  }

  /// Update product rating based on reviews
  Future<void> _updateProductRating(String productId) async {
    try {
      final stats = await getProductReviewStats(productId);
      await _firebaseService.productsCollection.doc(productId).update({
        'rating': stats['averageRating'],
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      print('Failed to update product rating: $e');
      // Don't throw here as this is a background operation
    }
  }

  /// Stream reviews for real-time updates
  Stream<List<Review>> streamProductReviews(
    String productId, {
    int limit = 20,
  }) {
    return reviewsCollection
        .where('productId', isEqualTo: productId)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Review.fromFirestore(doc))
            .toList());
  }
}
