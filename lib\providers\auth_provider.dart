import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';
import '../services/firebase_service.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final FirebaseService _firebaseService = FirebaseService();

  User? _user;
  bool _isLoading = false;
  String? _errorMessage;
  DocumentSnapshot? _userData;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;
  DocumentSnapshot? get userData => _userData;

  AuthProvider() {
    _firebaseService.authStateChanges.listen((User? user) {
      _user = user;
      if (user != null) {
        _loadUserData(user.uid);
      } else {
        _userData = null;
      }
      notifyListeners();
    });
  }

  Future<void> _loadUserData(String uid) async {
    try {
      _userData = await _authService.getCurrentUserData();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  Future<bool> signInWithGoogle() async {
    try {
      _setLoading(true);
      _clearError();

      final UserCredential? userCredential = await _authService
          .signInWithGoogle();

      _setLoading(false);
      return userCredential != null;
    } on FirebaseAuthException catch (e) {
      _setError(e.message ?? 'Google sign in failed');
      _setLoading(false);
      return false;
    } catch (e) {
      _setError('Sign in failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> signInWithEmailPassword(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      final UserCredential? userCredential = await _authService
          .signInWithEmailPassword(email, password);

      _setLoading(false);
      return userCredential != null;
    } on FirebaseAuthException catch (e) {
      _setError(e.message ?? 'Sign in failed');
      _setLoading(false);
      return false;
    } catch (e) {
      _setError('Sign in failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> signUpWithEmailPassword(
    String email,
    String password,
    String name,
    String studentId,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      final UserCredential? userCredential = await _authService
          .registerWithEmailPassword(email, password, name, studentId);

      _setLoading(false);
      return userCredential != null;
    } on FirebaseAuthException catch (e) {
      _setError(e.message ?? 'Sign up failed');
      _setLoading(false);
      return false;
    } catch (e) {
      _setError('Sign up failed: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _authService.signOut();
    } catch (e) {
      _setError('Sign out failed: ${e.toString()}');
    }
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await _authService.sendPasswordResetEmail(email);

      _setLoading(false);
      return true;
    } on FirebaseAuthException catch (e) {
      _setError(e.message ?? 'Failed to send password reset email');
      _setLoading(false);
      return false;
    } catch (e) {
      _setError('Failed to send password reset email: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
