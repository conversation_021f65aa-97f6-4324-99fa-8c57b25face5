import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/wishlist_item.dart';
import '../models/product.dart';
import '../services/wishlist_service.dart';

class WishlistProvider extends ChangeNotifier {
  final WishlistService _wishlistService = WishlistService();
  List<WishlistItem> _items = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<WishlistItem> get items => _items;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get itemCount => _items.length;

  // Check if product is in wishlist
  bool isInWishlist(String productId) {
    return _items.any((item) => item.productId == productId);
  }

  // Get wishlist item by product ID
  WishlistItem? getWishlistItem(String productId) {
    try {
      return _items.firstWhere((item) => item.productId == productId);
    } catch (e) {
      return null;
    }
  }

  // Get available items (products still in stock)
  List<WishlistItem> get availableItems {
    return _items.where((item) => item.isAvailable).toList();
  }

  // Get unavailable items (out of stock or inactive)
  List<WishlistItem> get unavailableItems {
    return _items.where((item) => !item.isAvailable).toList();
  }

  // Initialize wishlist (load from local storage and sync with server)
  Future<void> initializeWishlist() async {
    _setLoading(true);
    try {
      // Load from local storage first for immediate UI update
      await _loadFromLocalStorage();
      
      // Then sync with server
      await _syncWithServer();
    } catch (e) {
      _setError('Failed to initialize wishlist: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add item to wishlist
  Future<void> addToWishlist(Product product, {String? notes}) async {
    try {
      if (isInWishlist(product.id)) {
        // Item already in wishlist
        return;
      }

      final wishlistItem = WishlistItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        productId: product.id,
        product: product,
        addedAt: DateTime.now(),
        notes: notes,
      );
      
      _items.add(wishlistItem);
      await _saveToLocalStorage();
      await _wishlistService.addToWishlist(wishlistItem);
      notifyListeners();
    } catch (e) {
      _setError('Failed to add item to wishlist: $e');
    }
  }

  // Remove item from wishlist
  Future<void> removeFromWishlist(String productId) async {
    try {
      final itemIndex = _items.indexWhere((item) => item.productId == productId);
      if (itemIndex != -1) {
        final item = _items[itemIndex];
        _items.removeAt(itemIndex);
        await _saveToLocalStorage();
        await _wishlistService.removeFromWishlist(item.id);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to remove item from wishlist: $e');
    }
  }

  // Toggle item in wishlist (add if not present, remove if present)
  Future<void> toggleWishlist(Product product) async {
    if (isInWishlist(product.id)) {
      await removeFromWishlist(product.id);
    } else {
      await addToWishlist(product);
    }
  }

  // Update item notes
  Future<void> updateNotes(String productId, String notes) async {
    try {
      final itemIndex = _items.indexWhere((item) => item.productId == productId);
      if (itemIndex != -1) {
        final oldItem = _items[itemIndex];
        final updatedItem = oldItem.copyWith(notes: notes);
        _items[itemIndex] = updatedItem;
        await _saveToLocalStorage();
        await _wishlistService.updateWishlistItem(updatedItem);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update notes: $e');
    }
  }

  // Clear entire wishlist
  Future<void> clearWishlist() async {
    try {
      _items.clear();
      await _saveToLocalStorage();
      await _wishlistService.clearWishlist();
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear wishlist: $e');
    }
  }

  // Move item to cart (requires cart provider)
  Future<void> moveToCart(String productId, {required Function(Product, int) addToCartCallback}) async {
    try {
      final item = getWishlistItem(productId);
      if (item != null && item.isAvailable) {
        // Add to cart
        await addToCartCallback(item.product, 1);
        
        // Remove from wishlist
        await removeFromWishlist(productId);
      }
    } catch (e) {
      _setError('Failed to move item to cart: $e');
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Load wishlist from local storage
  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wishlistData = prefs.getString('wishlist_items');
      if (wishlistData != null) {
        final List<dynamic> itemsJson = json.decode(wishlistData);
        _items = itemsJson.map((json) => WishlistItem.fromMap(json)).toList();
        notifyListeners();
      }
    } catch (e) {
      print('Error loading wishlist from local storage: $e');
    }
  }

  // Save wishlist to local storage
  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final itemsJson = _items.map((item) => item.toMap()).toList();
      await prefs.setString('wishlist_items', json.encode(itemsJson));
    } catch (e) {
      print('Error saving wishlist to local storage: $e');
    }
  }

  // Sync with server
  Future<void> _syncWithServer() async {
    try {
      final serverItems = await _wishlistService.getWishlistItems();
      _items = serverItems;
      await _saveToLocalStorage();
      notifyListeners();
    } catch (e) {
      print('Error syncing wishlist with server: $e');
      // Continue with local data if server sync fails
    }
  }

  // Refresh wishlist from server
  Future<void> refreshWishlist() async {
    _setLoading(true);
    try {
      await _syncWithServer();
    } catch (e) {
      _setError('Failed to refresh wishlist: $e');
    } finally {
      _setLoading(false);
    }
  }
}
