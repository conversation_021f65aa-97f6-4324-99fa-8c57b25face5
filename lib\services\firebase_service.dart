import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

/// Central Firebase service class for dekuMArt app
/// Provides access to all Firebase services with proper initialization
class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  // Firebase service instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;

  // Getters for Firebase services
  FirebaseAuth get auth => _auth;
  FirebaseFirestore get firestore => _firestore;
  FirebaseStorage get storage => _storage;
  FirebaseMessaging get messaging => _messaging;

  /// Initialize Firebase services
  Future<void> initialize() async {
    try {
      // Configure Firestore settings
      _firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      // Request notification permissions
      await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      // Configure Firebase Messaging
      await _configureMessaging();

      print('Firebase services initialized successfully');
    } catch (e) {
      print('Error initializing Firebase services: $e');
      rethrow;
    }
  }

  /// Configure Firebase Cloud Messaging
  Future<void> _configureMessaging() async {
    try {
      // Get FCM token
      String? token = await _messaging.getToken();
      print('FCM Token: $token');

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('Received foreground message: ${message.notification?.title}');
        // Handle foreground notification display
      });

      // Handle background message taps
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print('Message clicked: ${message.notification?.title}');
        // Handle navigation when notification is tapped
      });

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    } catch (e) {
      print('Error configuring messaging: $e');
    }
  }

  /// Get current user
  User? get currentUser => _auth.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => _auth.currentUser != null;

  /// Get user authentication state stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Collection references for easy access
  CollectionReference get usersCollection => _firestore.collection('users');
  CollectionReference get productsCollection => _firestore.collection('products');
  CollectionReference get chatsCollection => _firestore.collection('chats');
  CollectionReference get communitiesCollection => _firestore.collection('communities');
  CollectionReference get postsCollection => _firestore.collection('posts');

  /// Storage references
  Reference get userAvatarsRef => _storage.ref().child('user_avatars');
  Reference get productImagesRef => _storage.ref().child('product_images');
  Reference get postImagesRef => _storage.ref().child('post_images');
}

/// Background message handler (must be top-level function)
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('Handling background message: ${message.messageId}');
  // Handle background message processing
}
