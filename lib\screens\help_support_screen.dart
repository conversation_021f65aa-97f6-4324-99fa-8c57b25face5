import 'package:flutter/material.dart';

class HelpSupportScreen extends StatefulWidget {
  const HelpSupportScreen({super.key});

  @override
  State<HelpSupportScreen> createState() => _HelpSupportScreenState();
}

class _HelpSupportScreenState extends State<HelpSupportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _issueController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  String _selectedIssueType = 'General';

  final List<String> _issueTypes = [
    'General',
    'Account Issues',
    'Payment Problems',
    'Technical Bug',
    'Inappropriate Content',
    'Safety Concerns',
    'Feature Request',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _issueController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'FAQ', icon: Icon(Icons.help_outline)),
            Tab(text: 'Contact', icon: Icon(Icons.contact_support)),
            Tab(text: 'Report', icon: Icon(Icons.report_problem)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildFAQTab(), _buildContactTab(), _buildReportTab()],
      ),
    );
  }

  Widget _buildFAQTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const Text(
          'Frequently Asked Questions',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        _buildFAQItem(
          'How do I create an account?',
          'You can create an account using your DeKUT email address. Simply tap "Sign Up" on the login screen and follow the instructions.',
        ),

        _buildFAQItem(
          'How do I post a product for sale?',
          'Tap the "+" button in the bottom navigation, then select "Post a Product". Fill in the details, add photos, and publish your listing.',
        ),

        _buildFAQItem(
          'Is it safe to meet buyers/sellers?',
          'Always meet in public places on campus during daylight hours. Bring a friend if possible and trust your instincts.',
        ),

        _buildFAQItem(
          'How do I report inappropriate content?',
          'Use the "Report" tab in this help section or tap the report button on any listing or user profile.',
        ),

        _buildFAQItem(
          'Can I edit my listing after posting?',
          'Yes, go to "My Listings" in the profile drawer and tap the edit button on any of your products.',
        ),

        _buildFAQItem(
          'How do I delete my account?',
          'Go to Settings > Privacy & Security > Delete Account. Note that this action is permanent.',
        ),

        _buildFAQItem(
          'What payment methods are accepted?',
          'dekuMArt facilitates connections between buyers and sellers. Payment arrangements are made directly between users.',
        ),

        _buildFAQItem(
          'How do I contact customer support?',
          'Use the "Contact" tab in this help section or email us <NAME_EMAIL>',
        ),
      ],
    );
  }

  Widget _buildContactTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Get in Touch',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          const Text(
            'We\'re here to help! Choose the best way to reach us:',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 24),

          _buildContactOption(
            icon: Icons.email,
            title: 'Email Support',
            subtitle: '<EMAIL>',
            description: 'Get help via email (24-48 hour response)',
            onTap: () => _launchEmail('<EMAIL>'),
          ),

          _buildContactOption(
            icon: Icons.phone,
            title: 'Phone Support',
            subtitle: '+254 700 123 456',
            description: 'Call us during business hours (8AM - 6PM)',
            onTap: () => _launchPhone('+254700123456'),
          ),

          _buildContactOption(
            icon: Icons.chat,
            title: 'Live Chat',
            subtitle: 'Chat with our team',
            description: 'Available Monday - Friday (9AM - 5PM)',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Live chat coming soon!')),
              );
            },
          ),

          _buildContactOption(
            icon: Icons.location_on,
            title: 'Visit Us',
            subtitle: 'DeKUT Main Campus',
            description: 'Student Affairs Office, 2nd Floor',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Office hours: Mon-Fri 8AM-5PM')),
              );
            },
          ),

          const Spacer(),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: Colors.blue.shade600),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'For urgent safety concerns, contact campus security immediately.',
                    style: TextStyle(
                      color: Colors.blue.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Report an Issue',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          const Text(
            'Help us maintain a safe and positive community by reporting any issues you encounter.',
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 24),

          // Issue Type Dropdown
          DropdownButtonFormField<String>(
            initialValue: _selectedIssueType,
            decoration: const InputDecoration(
              labelText: 'Issue Type',
              border: OutlineInputBorder(),
            ),
            items: _issueTypes.map((type) {
              return DropdownMenuItem(value: type, child: Text(type));
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedIssueType = value!;
              });
            },
          ),
          const SizedBox(height: 16),

          // Email Field
          TextFormField(
            controller: _emailController,
            decoration: const InputDecoration(
              labelText: 'Your Email (optional)',
              hintText: 'For follow-up communication',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),

          // Issue Description
          TextFormField(
            controller: _issueController,
            decoration: const InputDecoration(
              labelText: 'Describe the Issue',
              hintText: 'Please provide as much detail as possible...',
              border: OutlineInputBorder(),
            ),
            maxLines: 5,
            maxLength: 500,
          ),
          const SizedBox(height: 24),

          // Submit Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _submitReport,
              icon: const Icon(Icons.send),
              label: const Text('Submit Report'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7D32),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, color: Colors.orange.shade600),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'For immediate safety concerns, contact campus security or local authorities directly.',
                    style: TextStyle(
                      color: Colors.orange.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              answer,
              style: const TextStyle(fontSize: 14, height: 1.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: const Color(0xFF2E7D32).withValues(alpha: 0.1),
          child: Icon(icon, color: const Color(0xFF2E7D32)),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(subtitle, style: const TextStyle(fontWeight: FontWeight.w500)),
            Text(description, style: TextStyle(color: Colors.grey.shade600)),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _submitReport() {
    if (_issueController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please describe the issue')),
      );
      return;
    }

    // TODO: Implement actual report submission
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Report submitted successfully. We\'ll review it shortly.',
        ),
        backgroundColor: Colors.green,
      ),
    );

    // Clear the form
    _issueController.clear();
    _emailController.clear();
    setState(() {
      _selectedIssueType = 'General';
    });
  }

  Future<void> _launchEmail(String email) async {
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Email support: $email')));
    }
  }

  Future<void> _launchPhone(String phone) async {
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Call support: $phone')));
    }
  }
}
