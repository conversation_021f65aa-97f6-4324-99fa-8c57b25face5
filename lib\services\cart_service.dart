import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/cart_item.dart';
import '../services/firebase_service.dart';

class CartService {
  final FirebaseService _firebaseService = FirebaseService();
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get current user's cart collection reference
  CollectionReference get _userCartCollection {
    final userId = _auth.currentUser?.uid;
    if (userId == null) {
      throw Exception('User not authenticated');
    }
    return _firebaseService.firestore
        .collection('users')
        .doc(userId)
        .collection('cart');
  }

  /// Add item to cart
  Future<void> addToCart(CartItem item) async {
    try {
      await _userCartCollection.doc(item.id).set(item.toFirestore());
    } catch (e) {
      throw Exception('Failed to add item to cart: $e');
    }
  }

  /// Get all cart items for current user
  Future<List<CartItem>> getCartItems() async {
    try {
      final querySnapshot = await _userCartCollection
          .orderBy('addedAt', descending: true)
          .get();
      
      return querySnapshot.docs
          .map((doc) => CartItem.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get cart items: $e');
    }
  }

  /// Get cart items stream for real-time updates
  Stream<List<CartItem>> getCartItemsStream() {
    try {
      return _userCartCollection
          .orderBy('addedAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => CartItem.fromFirestore(doc))
              .toList());
    } catch (e) {
      throw Exception('Failed to get cart items stream: $e');
    }
  }

  /// Update cart item
  Future<void> updateCartItem(CartItem item) async {
    try {
      await _userCartCollection.doc(item.id).update(item.toFirestore());
    } catch (e) {
      throw Exception('Failed to update cart item: $e');
    }
  }

  /// Remove item from cart
  Future<void> removeFromCart(String itemId) async {
    try {
      await _userCartCollection.doc(itemId).delete();
    } catch (e) {
      throw Exception('Failed to remove item from cart: $e');
    }
  }

  /// Clear entire cart
  Future<void> clearCart() async {
    try {
      final batch = _firebaseService.firestore.batch();
      final querySnapshot = await _userCartCollection.get();
      
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
    } catch (e) {
      throw Exception('Failed to clear cart: $e');
    }
  }

  /// Get cart item by product ID
  Future<CartItem?> getCartItemByProductId(String productId) async {
    try {
      final querySnapshot = await _userCartCollection
          .where('productId', isEqualTo: productId)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isNotEmpty) {
        return CartItem.fromFirestore(querySnapshot.docs.first);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get cart item: $e');
    }
  }

  /// Update item quantity
  Future<void> updateQuantity(String itemId, int newQuantity) async {
    try {
      await _userCartCollection.doc(itemId).update({
        'quantity': newQuantity,
      });
    } catch (e) {
      throw Exception('Failed to update quantity: $e');
    }
  }

  /// Get cart summary (total items, total price, etc.)
  Future<Map<String, dynamic>> getCartSummary() async {
    try {
      final items = await getCartItems();
      
      final totalItems = items.length;
      final totalQuantity = items.fold<int>(0, (sum, item) => sum + item.quantity);
      final totalPrice = items.fold<double>(0.0, (sum, item) => sum + item.totalPrice);
      final subtotal = totalPrice;
      final tax = totalPrice * 0.16; // 16% VAT
      final shipping = totalPrice > 1000 ? 0 : 200; // Free shipping over KSh 1000
      final grandTotal = subtotal + tax + shipping;
      
      return {
        'totalItems': totalItems,
        'totalQuantity': totalQuantity,
        'subtotal': subtotal,
        'tax': tax,
        'shipping': shipping,
        'grandTotal': grandTotal,
      };
    } catch (e) {
      throw Exception('Failed to get cart summary: $e');
    }
  }

  /// Check if product is in cart
  Future<bool> isProductInCart(String productId) async {
    try {
      final item = await getCartItemByProductId(productId);
      return item != null;
    } catch (e) {
      return false;
    }
  }

  /// Sync cart with updated product information
  Future<void> syncCartWithProducts() async {
    try {
      final items = await getCartItems();
      final batch = _firebaseService.firestore.batch();
      
      for (final item in items) {
        // Get updated product information
        final productDoc = await _firebaseService.productsCollection
            .doc(item.productId)
            .get();
        
        if (productDoc.exists) {
          final updatedProduct = productDoc.data() as Map<String, dynamic>;
          
          // Update cart item with latest product info
          final updatedCartItem = item.copyWith(
            unitPrice: (updatedProduct['price'] ?? item.unitPrice).toDouble(),
          );
          
          batch.update(
            _userCartCollection.doc(item.id),
            updatedCartItem.toFirestore(),
          );
        } else {
          // Product no longer exists, remove from cart
          batch.delete(_userCartCollection.doc(item.id));
        }
      }
      
      await batch.commit();
    } catch (e) {
      throw Exception('Failed to sync cart with products: $e');
    }
  }

  /// Validate cart items (check stock, availability, etc.)
  Future<Map<String, dynamic>> validateCart() async {
    try {
      final items = await getCartItems();
      final List<String> outOfStockItems = [];
      final List<String> unavailableItems = [];
      final List<String> priceChangedItems = [];
      
      for (final item in items) {
        final productDoc = await _firebaseService.productsCollection
            .doc(item.productId)
            .get();
        
        if (!productDoc.exists) {
          unavailableItems.add(item.product.title);
          continue;
        }
        
        final productData = productDoc.data() as Map<String, dynamic>;
        final currentStock = productData['stock'] ?? 0;
        final currentPrice = (productData['price'] ?? 0.0).toDouble();
        final isActive = productData['isActive'] ?? false;
        
        if (!isActive) {
          unavailableItems.add(item.product.title);
        } else if (currentStock < item.quantity) {
          outOfStockItems.add(item.product.title);
        } else if (currentPrice != item.unitPrice) {
          priceChangedItems.add(item.product.title);
        }
      }
      
      return {
        'isValid': outOfStockItems.isEmpty && unavailableItems.isEmpty,
        'outOfStockItems': outOfStockItems,
        'unavailableItems': unavailableItems,
        'priceChangedItems': priceChangedItems,
      };
    } catch (e) {
      throw Exception('Failed to validate cart: $e');
    }
  }
}
