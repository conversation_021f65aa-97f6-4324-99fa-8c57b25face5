import 'package:cloud_firestore/cloud_firestore.dart';

class SellerProfile {
  final String id;
  final String userId;
  final String name;
  final String? avatar;
  final String email;
  final String? phone;
  final String? bio;
  final String location;
  final DateTime joinedAt;
  final DateTime lastSeen;
  final bool isOnline;
  final bool isVerified;
  final double rating;
  final int totalRatings;
  final int totalSales;
  final int totalProducts;
  final double responseRate;
  final String responseTime; // e.g., "< 1 hour", "within 24 hours"
  final List<String> specialties;
  final Map<String, dynamic> businessInfo;
  final Map<String, dynamic> socialLinks;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  const SellerProfile({
    required this.id,
    required this.userId,
    required this.name,
    this.avatar,
    required this.email,
    this.phone,
    this.bio,
    required this.location,
    required this.joinedAt,
    required this.lastSeen,
    this.isOnline = false,
    this.isVerified = false,
    this.rating = 0.0,
    this.totalRatings = 0,
    this.totalSales = 0,
    this.totalProducts = 0,
    this.responseRate = 0.0,
    this.responseTime = 'Unknown',
    this.specialties = const [],
    this.businessInfo = const {},
    this.socialLinks = const {},
    this.isActive = true,
    this.metadata,
  });

  factory SellerProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SellerProfile(
      id: doc.id,
      userId: data['userId'] ?? '',
      name: data['name'] ?? '',
      avatar: data['avatar'],
      email: data['email'] ?? '',
      phone: data['phone'],
      bio: data['bio'],
      location: data['location'] ?? '',
      joinedAt: (data['joinedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastSeen: (data['lastSeen'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isOnline: data['isOnline'] ?? false,
      isVerified: data['isVerified'] ?? false,
      rating: (data['rating'] ?? 0).toDouble(),
      totalRatings: data['totalRatings'] ?? 0,
      totalSales: data['totalSales'] ?? 0,
      totalProducts: data['totalProducts'] ?? 0,
      responseRate: (data['responseRate'] ?? 0).toDouble(),
      responseTime: data['responseTime'] ?? 'Unknown',
      specialties: List<String>.from(data['specialties'] ?? []),
      businessInfo: Map<String, dynamic>.from(data['businessInfo'] ?? {}),
      socialLinks: Map<String, dynamic>.from(data['socialLinks'] ?? {}),
      isActive: data['isActive'] ?? true,
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'name': name,
      'avatar': avatar,
      'email': email,
      'phone': phone,
      'bio': bio,
      'location': location,
      'joinedAt': Timestamp.fromDate(joinedAt),
      'lastSeen': Timestamp.fromDate(lastSeen),
      'isOnline': isOnline,
      'isVerified': isVerified,
      'rating': rating,
      'totalRatings': totalRatings,
      'totalSales': totalSales,
      'totalProducts': totalProducts,
      'responseRate': responseRate,
      'responseTime': responseTime,
      'specialties': specialties,
      'businessInfo': businessInfo,
      'socialLinks': socialLinks,
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  SellerProfile copyWith({
    String? id,
    String? userId,
    String? name,
    String? avatar,
    String? email,
    String? phone,
    String? bio,
    String? location,
    DateTime? joinedAt,
    DateTime? lastSeen,
    bool? isOnline,
    bool? isVerified,
    double? rating,
    int? totalRatings,
    int? totalSales,
    int? totalProducts,
    double? responseRate,
    String? responseTime,
    List<String>? specialties,
    Map<String, dynamic>? businessInfo,
    Map<String, dynamic>? socialLinks,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return SellerProfile(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      bio: bio ?? this.bio,
      location: location ?? this.location,
      joinedAt: joinedAt ?? this.joinedAt,
      lastSeen: lastSeen ?? this.lastSeen,
      isOnline: isOnline ?? this.isOnline,
      isVerified: isVerified ?? this.isVerified,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
      totalSales: totalSales ?? this.totalSales,
      totalProducts: totalProducts ?? this.totalProducts,
      responseRate: responseRate ?? this.responseRate,
      responseTime: responseTime ?? this.responseTime,
      specialties: specialties ?? this.specialties,
      businessInfo: businessInfo ?? this.businessInfo,
      socialLinks: socialLinks ?? this.socialLinks,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  String get formattedRating => rating.toStringAsFixed(1);
  
  String get ratingDisplay => totalRatings > 0 ? '$formattedRating★ ($totalRatings)' : 'No ratings yet';

  @override
  String toString() {
    return 'SellerProfile(id: $id, name: $name, rating: $rating, totalSales: $totalSales)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SellerProfile && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
