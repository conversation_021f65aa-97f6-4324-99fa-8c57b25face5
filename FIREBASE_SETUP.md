# Firebase Setup Guide for dekuMArt

## Overview
This guide will help you complete the Firebase setup for the dekuMArt app. The app structure and Firebase integration code are already in place, but you need to configure your Firebase project and add the configuration files.

## Prerequisites
- Flutter SDK installed
- Firebase CLI installed (`npm install -g firebase-tools`)
- Google account for Firebase Console access

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Project name: `dekumart-project` (or your preferred name)
4. Enable Google Analytics (recommended)
5. Choose your Analytics account or create a new one

## Step 2: Configure Firebase Services

### Enable Authentication
1. In Firebase Console, go to **Authentication** > **Sign-in method**
2. Enable the following providers:
   - **Email/Password**: Enable this provider
   - **Google**: Enable and configure (download config files after setup)
3. Go to **Authentication** > **Settings** > **Authorized domains**
4. Add your domain if deploying to web (optional for now)

### Setup Firestore Database
1. Go to **Firestore Database**
2. Click "Create database"
3. Choose "Start in test mode" (you can secure it later)
4. Select a location close to your users (e.g., us-central1)

### Setup Storage
1. Go to **Storage**
2. Click "Get started"
3. Start in test mode
4. Choose the same location as Firestore

### Setup Cloud Messaging (Optional)
1. Go to **Cloud Messaging**
2. No additional setup required initially

## Step 3: Add Android App

1. In Firebase Console, click "Add app" > Android
2. **Package name**: `com.dekut.dekumart_app`
3. **App nickname**: `dekuMArt Android`
4. **Debug signing certificate SHA-1**: Get this by running:
   ```bash
   cd android
   ./gradlew signingReport
   ```
   Look for the SHA1 under "Variant: debug"
5. Download `google-services.json`
6. Place it in `android/app/google-services.json` (replace the template)

## Step 4: Add iOS App

1. In Firebase Console, click "Add app" > iOS
2. **Bundle ID**: `com.dekut.dekumartApp`
3. **App nickname**: `dekuMArt iOS`
4. Download `GoogleService-Info.plist`
5. Place it in `ios/Runner/GoogleService-Info.plist` (replace the template)

## Step 5: Update Firebase Options

1. Install FlutterFire CLI:
   ```bash
   dart pub global activate flutterfire_cli
   ```

2. Configure FlutterFire (this will update firebase_options.dart):
   ```bash
   flutterfire configure
   ```
   - Select your Firebase project
   - Select platforms (Android, iOS, Web if needed)
   - This will automatically update `lib/firebase_options.dart` with real values

## Step 6: Test the Setup

1. Get dependencies:
   ```bash
   flutter pub get
   ```

2. Run the app:
   ```bash
   flutter run
   ```

3. Test authentication:
   - Try signing up with a @dkut.ac.ke email
   - Try Google Sign-In
   - Check Firebase Console > Authentication > Users to see if users are created

## Step 7: Configure Firestore Security Rules

Replace the default rules in Firestore with these basic rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Products can be read by anyone, written by authenticated users
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Chats can only be accessed by participants
    match /chats/{chatId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
    }
    
    // Community posts can be read by anyone, written by authenticated users
    match /posts/{postId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Communities can be read by anyone, written by authenticated users
    match /communities/{communityId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

## Step 8: Configure Storage Security Rules

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /user_avatars/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /product_images/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /post_images/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

## Troubleshooting

### Common Issues:

1. **Build errors**: Make sure you've placed the config files in the correct locations
2. **Authentication not working**: Check that you've enabled the sign-in methods in Firebase Console
3. **Network errors**: Ensure your app has internet permissions (should be default in Flutter)

### Email Domain Restriction

The app is configured to only allow @dkut.ac.ke email addresses. To test with other emails during development, you can temporarily modify the validation in `lib/services/auth_service.dart`.

## Next Steps

After Firebase is set up:
1. Test all authentication methods
2. Create some test data in Firestore
3. Test image uploads to Storage
4. Configure push notifications if needed
5. Set up proper security rules for production

## Files Modified/Created

- ✅ `lib/firebase_options.dart` - Firebase configuration
- ✅ `lib/services/firebase_service.dart` - Firebase service wrapper
- ✅ `lib/services/auth_service.dart` - Authentication service
- ✅ `lib/providers/auth_provider.dart` - Updated to use new services
- ✅ `lib/main.dart` - Firebase initialization
- ✅ `android/build.gradle.kts` - Google Services plugin
- ✅ `android/app/build.gradle.kts` - Google Services plugin and package name
- 📝 `android/app/google-services.json` - **YOU NEED TO ADD THIS**
- 📝 `ios/Runner/GoogleService-Info.plist` - **YOU NEED TO ADD THIS**

The ✅ files are already configured. The 📝 files need to be downloaded from Firebase Console.
