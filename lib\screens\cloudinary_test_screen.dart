import 'package:flutter/material.dart';
import '../services/cloudinary_service.dart';

class CloudinaryTestScreen extends StatefulWidget {
  const CloudinaryTestScreen({super.key});

  @override
  State<CloudinaryTestScreen> createState() => _CloudinaryTestScreenState();
}

class _CloudinaryTestScreenState extends State<CloudinaryTestScreen> {
  final CloudinaryService _cloudinaryService = CloudinaryService();
  bool _isLoading = false;
  String _result = '';

  Future<void> _testUploadPreset() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing upload preset...';
    });

    try {
      final result = await _cloudinaryService.testUploadPreset();
      setState(() {
        _result = _formatResult(result);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'Error testing upload preset: $e';
        _isLoading = false;
      });
    }
  }

  String _formatResult(Map<String, dynamic> result) {
    final buffer = StringBuffer();
    buffer.writeln('=== CLOUDINARY TEST RESULT ===\n');
    
    buffer.writeln('Success: ${result['success']}');
    buffer.writeln('Cloud Name: ${result['cloudName']}');
    buffer.writeln('Upload Preset: ${result['preset']}\n');
    
    if (result['success'] == true) {
      buffer.writeln('✅ ${result['message']}');
      buffer.writeln('\nYour Cloudinary configuration is working correctly!');
    } else {
      buffer.writeln('❌ Error: ${result['error']}');
      buffer.writeln('\nPossible solutions:');
      buffer.writeln('1. Check if upload preset "dekumart_products" exists');
      buffer.writeln('2. Verify upload preset is set to "Unsigned"');
      buffer.writeln('3. Check cloud name is correct');
      buffer.writeln('4. Ensure upload preset allows image uploads');
      
      if (result['details'] != null) {
        buffer.writeln('\nDetailed error:');
        buffer.writeln(result['details'].toString());
      }
    }
    
    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cloudinary Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Cloudinary Configuration Test',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This will test if your Cloudinary upload preset is configured correctly.',
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testUploadPreset,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                        child: _isLoading
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text('Testing...'),
                                ],
                              )
                            : const Text('Test Upload Preset'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_result.isNotEmpty)
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Test Results',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Text(
                              _result,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
            Card(
              color: Colors.orange.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.orange.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Setup Instructions',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'If the test fails, follow these steps:\n\n'
                      '1. Go to cloudinary.com and log in\n'
                      '2. Navigate to Settings > Upload\n'
                      '3. Click "Add upload preset"\n'
                      '4. Set preset name: "dekumart_products"\n'
                      '5. Set signing mode: "Unsigned"\n'
                      '6. Set folder: "dekumart/products"\n'
                      '7. Allow formats: jpg, png, jpeg, webp\n'
                      '8. Set max file size: 10MB\n'
                      '9. Save the preset',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
