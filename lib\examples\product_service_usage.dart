// This file demonstrates how to use the ProductService in your app
// You can copy these examples into your actual screens/widgets

import 'package:flutter/material.dart';
import '../services/product_service.dart';
import '../models/product.dart';

class ProductServiceUsageExamples {
  final ProductService _productService = ProductService();

  /// Example 1: Fetch and display all products
  Future<List<Product>> fetchAllProducts() async {
    try {
      final products = await _productService.getAllProducts(limit: 20);
      return products;
    } catch (e) {
      print('Error fetching products: $e');
      return [];
    }
  }

  /// Example 2: Search products
  Future<List<Product>> searchProducts(String query) async {
    try {
      final products = await _productService.searchProducts(query, limit: 10);
      return products;
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  /// Example 3: Get products by category
  Future<List<Product>> getElectronicsProducts() async {
    try {
      final products = await _productService.getProductsByCategory('Electronics');
      return products;
    } catch (e) {
      print('Error fetching electronics: $e');
      return [];
    }
  }

  /// Example 4: Get user's products
  Future<List<Product>> getUserProducts(String userId) async {
    try {
      final products = await _productService.getProductsBySeller(userId);
      return products;
    } catch (e) {
      print('Error fetching user products: $e');
      return [];
    }
  }

  /// Example 5: Update product stock
  Future<void> updateProductStock(String productId, int newStock) async {
    try {
      await _productService.updateStock(productId, newStock);
      print('Stock updated successfully');
    } catch (e) {
      print('Error updating stock: $e');
    }
  }

  /// Example 6: Delete product
  Future<void> deleteProduct(String productId) async {
    try {
      await _productService.deleteProduct(productId);
      print('Product deleted successfully');
    } catch (e) {
      print('Error deleting product: $e');
    }
  }

  /// Example 7: Get seller statistics
  Future<Map<String, dynamic>> getSellerStats(String sellerId) async {
    try {
      final stats = await _productService.getSellerStats(sellerId);
      return stats;
    } catch (e) {
      print('Error fetching seller stats: $e');
      return {};
    }
  }

  /// Example 8: Real-time products stream
  Stream<List<Product>> getProductsStream({String? category}) {
    return _productService.getProductsStream(category: category);
  }

  /// Example 9: Get products by price range
  Future<List<Product>> getProductsByPriceRange(double minPrice, double maxPrice) async {
    try {
      final products = await _productService.getProductsByPriceRange(
        minPrice: minPrice,
        maxPrice: maxPrice,
      );
      return products;
    } catch (e) {
      print('Error fetching products by price range: $e');
      return [];
    }
  }

  /// Example 10: Increment view count when user views product
  Future<void> viewProduct(String productId) async {
    try {
      await _productService.incrementViewCount(productId);
    } catch (e) {
      print('Error incrementing view count: $e');
    }
  }
}

/// Example Widget: Products List Screen
class ProductsListScreen extends StatefulWidget {
  const ProductsListScreen({super.key});

  @override
  State<ProductsListScreen> createState() => _ProductsListScreenState();
}

class _ProductsListScreenState extends State<ProductsListScreen> {
  final ProductService _productService = ProductService();
  List<Product> _products = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  Future<void> _loadProducts() async {
    try {
      final products = await _productService.getAllProducts();
      setState(() {
        _products = products;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading products: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Products')),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: _products.length,
              itemBuilder: (context, index) {
                final product = _products[index];
                return ListTile(
                  leading: product.images.isNotEmpty
                      ? Image.network(
                          product.images.first,
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                        )
                      : const Icon(Icons.image),
                  title: Text(product.title),
                  subtitle: Text('KSh ${product.price}'),
                  trailing: Text(product.category),
                  onTap: () {
                    // Increment view count when tapped
                    _productService.incrementViewCount(product.id);
                    // Navigate to product detail
                    // Navigator.push(context, MaterialPageRoute(
                    //   builder: (context) => ProductDetailScreen(product: product),
                    // ));
                  },
                );
              },
            ),
    );
  }
}

/// Example Widget: Real-time Products Stream
class RealTimeProductsWidget extends StatelessWidget {
  final String? category;
  
  const RealTimeProductsWidget({super.key, this.category});

  @override
  Widget build(BuildContext context) {
    final productService = ProductService();
    
    return StreamBuilder<List<Product>>(
      stream: productService.getProductsStream(category: category),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }
        
        final products = snapshot.data ?? [];
        
        if (products.isEmpty) {
          return const Center(child: Text('No products found'));
        }
        
        return ListView.builder(
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return Card(
              child: ListTile(
                title: Text(product.title),
                subtitle: Text('KSh ${product.price} • ${product.location}'),
                trailing: Text('${product.viewCount} views'),
              ),
            );
          },
        );
      },
    );
  }
}

/// Example: Search functionality
class ProductSearchDelegate extends SearchDelegate<Product?> {
  final ProductService _productService = ProductService();

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () => query = '',
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return FutureBuilder<List<Product>>(
      future: _productService.searchProducts(query),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final products = snapshot.data ?? [];
        
        return ListView.builder(
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return ListTile(
              title: Text(product.title),
              subtitle: Text('KSh ${product.price}'),
              onTap: () => close(context, product),
            );
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return const Center(child: Text('Enter a search term'));
    }
    
    return buildResults(context);
  }
}
