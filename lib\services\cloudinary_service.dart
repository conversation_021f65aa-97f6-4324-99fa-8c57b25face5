import 'dart:io';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import '../config/cloudinary_config.dart';

class CloudinaryService {
  static String get _cloudName => CloudinaryConfig.cloudName;
  static String get _apiKey => CloudinaryConfig.apiKey;
  static String get _apiSecret => CloudinaryConfig.apiSecret;
  static String get _uploadPreset => CloudinaryConfig.uploadPreset;

  final Dio _dio = Dio();

  /// Upload a single image to Cloudinary with progress tracking
  Future<CloudinaryUploadResult> uploadImage(
    File imageFile, {
    Function(double)? onProgress,
    String? folder,
  }) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

      // Create form data - simplified for unsigned upload
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          imageFile.path,
          filename: 'product_${timestamp}_${imageFile.path.split('/').last}',
        ),
        'upload_preset': _uploadPreset,
        // Remove conflicting parameters for unsigned upload
      });

      // Upload URL
      final uploadUrl =
          'https://api.cloudinary.com/v1_1/$_cloudName/image/upload';

      // Make the upload request with progress tracking
      final response = await _dio.post(
        uploadUrl,
        data: formData,
        onSendProgress: (sent, total) {
          if (onProgress != null && total > 0) {
            final progress = sent / total;
            onProgress(progress);
          }
        },
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return CloudinaryUploadResult(
          success: true,
          publicId: data['public_id'],
          secureUrl: data['secure_url'],
          url: data['url'],
          width: data['width'],
          height: data['height'],
          format: data['format'],
          bytes: data['bytes'],
        );
      } else {
        // Get more detailed error information
        String errorMessage =
            'Upload failed with status: ${response.statusCode}';
        if (response.data != null) {
          try {
            final errorData = response.data;
            if (errorData['error'] != null) {
              errorMessage +=
                  ' - ${errorData['error']['message'] ?? errorData['error']}';
            }
          } catch (e) {
            // If we can't parse the error, use the original message
          }
        }

        return CloudinaryUploadResult(success: false, error: errorMessage);
      }
    } catch (e) {
      return CloudinaryUploadResult(success: false, error: 'Upload error: $e');
    }
  }

  /// Upload multiple images with individual progress tracking
  Future<List<CloudinaryUploadResult>> uploadMultipleImages(
    List<File> imageFiles, {
    Function(int index, double progress)? onProgress,
    String? folder,
  }) async {
    final results = <CloudinaryUploadResult>[];

    for (int i = 0; i < imageFiles.length; i++) {
      final result = await uploadImage(
        imageFiles[i],
        onProgress: (progress) {
          onProgress?.call(i, progress);
        },
        folder: folder,
      );
      results.add(result);
    }

    return results;
  }

  /// Delete an image from Cloudinary
  Future<bool> deleteImage(String publicId) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final signature = _generateSignature({
        'public_id': publicId,
        'timestamp': timestamp,
      });

      final formData = FormData.fromMap({
        'public_id': publicId,
        'timestamp': timestamp,
        'api_key': _apiKey,
        'signature': signature,
      });

      final deleteUrl =
          'https://api.cloudinary.com/v1_1/$_cloudName/image/destroy';

      final response = await _dio.post(deleteUrl, data: formData);

      return response.statusCode == 200 && response.data['result'] == 'ok';
    } catch (e) {
      return false;
    }
  }

  /// Generate signature for authenticated requests
  String _generateSignature(Map<String, String> params) {
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );

    final paramString = sortedParams.entries
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');

    final stringToSign = '$paramString$_apiSecret';
    final bytes = utf8.encode(stringToSign);
    final digest = sha1.convert(bytes);

    return digest.toString();
  }

  /// Get optimized image URL with transformations
  String getOptimizedImageUrl(
    String publicId, {
    int? width,
    int? height,
    String quality = 'auto:good',
    String format = 'auto',
  }) {
    final transformations = <String>[];

    if (width != null) transformations.add('w_$width');
    if (height != null) transformations.add('h_$height');
    transformations.add('q_$quality');
    transformations.add('f_$format');

    final transformString = transformations.join(',');
    return 'https://res.cloudinary.com/$_cloudName/image/upload/$transformString/$publicId';
  }

  /// Test upload preset configuration
  Future<Map<String, dynamic>> testUploadPreset() async {
    try {
      // Test with a minimal request to check if upload preset exists
      final formData = FormData.fromMap({
        'upload_preset': _uploadPreset,
        'file':
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 transparent PNG
      });

      final uploadUrl =
          'https://api.cloudinary.com/v1_1/$_cloudName/image/upload';

      final response = await _dio.post(
        uploadUrl,
        data: formData,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
          validateStatus: (status) => status! < 500,
        ),
      );

      if (response.statusCode == 200) {
        // Delete the test image
        final publicId = response.data['public_id'];
        await deleteImage(publicId);

        return {
          'success': true,
          'message': 'Upload preset is working correctly',
          'preset': _uploadPreset,
          'cloudName': _cloudName,
        };
      } else {
        return {
          'success': false,
          'error':
              'Upload preset test failed with status: ${response.statusCode}',
          'details': response.data,
          'preset': _uploadPreset,
          'cloudName': _cloudName,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Upload preset test error: $e',
        'preset': _uploadPreset,
        'cloudName': _cloudName,
      };
    }
  }
}

class CloudinaryUploadResult {
  final bool success;
  final String? publicId;
  final String? secureUrl;
  final String? url;
  final int? width;
  final int? height;
  final String? format;
  final int? bytes;
  final String? error;

  CloudinaryUploadResult({
    required this.success,
    this.publicId,
    this.secureUrl,
    this.url,
    this.width,
    this.height,
    this.format,
    this.bytes,
    this.error,
  });

  @override
  String toString() {
    if (success) {
      return 'CloudinaryUploadResult(success: true, publicId: $publicId, url: $secureUrl)';
    } else {
      return 'CloudinaryUploadResult(success: false, error: $error)';
    }
  }
}
